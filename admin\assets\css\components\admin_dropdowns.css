/**
 * Admin Dropdowns CSS
 *
 * This file contains styles for dropdown menus in the admin panel.
 */

/* Dropdown Container */
.dropdown {
  position: relative;
  display: inline-block;
}

/* Dropdown Toggle */
.dropdown-toggle {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  cursor: pointer;
}

.dropdown-toggle::after {
  content: "▼";
  font-size: 8px;
  margin-top: 2px;
  transition: transform var(--transition-fast) ease;
}

.dropdown.show .dropdown-toggle::after {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-index-dropdown);
  display: none;
  min-width: 180px;
  padding: var(--spacing-1) 0;
  margin-top: var(--spacing-1);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  opacity: 0;
  transform: translateY(10px);
  transition: opacity var(--transition-fast) ease, transform var(--transition-fast) ease;
}

.dropdown.show .dropdown-menu {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

/* Dropdown Item */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  white-space: nowrap;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: var(--gray-50);
  color: var(--text-dark);
}

.dropdown-item.active {
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.dropdown-item.disabled {
  color: var(--text-muted);
  pointer-events: none;
  background-color: transparent;
}

/* Dropdown Item Icon */
.dropdown-item-icon {
  font-size: var(--font-size-base);
  color: var(--text-light);
  width: 20px;
  text-align: center;
}

.dropdown-item:hover .dropdown-item-icon {
  color: var(--primary-color);
}

.dropdown-item.active .dropdown-item-icon {
  color: var(--primary-color);
}

/* Dropdown Divider */
.dropdown-divider {
  height: 1px;
  margin: var(--spacing-1) 0;
  background-color: var(--border-color);
}

/* Dropdown Header */
.dropdown-header {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  margin-bottom: 0;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  white-space: nowrap;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.5px;
}

/* Dropdown Text */
.dropdown-text {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

/* Dropdown Menu Positions */
.dropdown-menu-right {
  left: auto;
  right: 0;
}

.dropdown-menu-center {
  left: 50%;
  transform: translateX(-50%) translateY(10px);
}

.dropdown.show .dropdown-menu-center {
  transform: translateX(-50%) translateY(0);
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--spacing-1);
  transform: translateY(-10px);
}

.dropup.show .dropdown-menu {
  transform: translateY(0);
}

.dropright .dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--spacing-1);
  transform: translateX(10px);
}

.dropright.show .dropdown-menu {
  transform: translateX(0);
}

.dropleft .dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--spacing-1);
  transform: translateX(-10px);
}

.dropleft.show .dropdown-menu {
  transform: translateX(0);
}

/* Dropdown Menu Sizes */
.dropdown-menu-sm {
  min-width: 120px;
  font-size: var(--font-size-xs);
}

.dropdown-menu-sm .dropdown-item {
  padding: var(--spacing-1) var(--spacing-3);
}

.dropdown-menu-lg {
  min-width: 240px;
}

.dropdown-menu-xl {
  min-width: 320px;
}

/* Dropdown with Form */
.dropdown-form {
  padding: var(--spacing-3);
  min-width: 240px;
}

.dropdown-form .form-group:last-child {
  margin-bottom: 0;
}

/* Dropdown with Search */
.dropdown-search {
  padding: var(--spacing-2);
  position: sticky;
  top: 0;
  background-color: var(--white);
  z-index: 1;
  border-bottom: 1px solid var(--border-light);
}

.dropdown-search-input {
  width: 100%;
  height: 34px;
  padding: var(--spacing-1) var(--spacing-1) var(--spacing-1) var(--spacing-7);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--gray-50);
  color: var(--text-color);
  font-size: var(--font-size-sm);
}

.dropdown-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: var(--white);
  box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.15);
}

.dropdown-search-icon {
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--font-size-base);
}

/* Dropdown with Scrollable Content */
.dropdown-menu-scrollable {
  max-height: 300px;
  overflow-y: auto;
}

/* Dropdown with Checkbox */
.dropdown-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dropdown-checkbox-input {
  width: 16px;
  height: 16px;
}

/* Dropdown with Radio */
.dropdown-radio {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.dropdown-radio-input {
  width: 16px;
  height: 16px;
}

/* Dropdown with Icons Only */
.dropdown-icons .dropdown-item {
  justify-content: center;
  padding: var(--spacing-2);
}

.dropdown-icons .dropdown-item-icon {
  margin: 0;
  width: auto;
  font-size: var(--font-size-lg);
}

.dropdown-icons .dropdown-item-text {
  display: none;
}

/* Dropdown with Grid */
.dropdown-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-1);
  padding: var(--spacing-2);
  min-width: 240px;
}

.dropdown-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  text-align: center;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast) ease;
  cursor: pointer;
}

.dropdown-grid-item:hover {
  background-color: var(--gray-50);
}

.dropdown-grid-icon {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-1);
  color: var(--text-light);
}

.dropdown-grid-text {
  font-size: var(--font-size-xs);
  color: var(--text-color);
}

/* Dropdown with Notifications */
.dropdown-notifications {
  min-width: 300px;
  padding: 0;
}

.dropdown-notifications-header {
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-notifications-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-dark);
  margin: 0;
}

.dropdown-notifications-badge {
  min-width: 20px;
  height: 20px;
  padding: 0 var(--spacing-1);
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  line-height: 1;
  border-radius: var(--radius-full);
  background-color: var(--primary-color);
  color: var(--secondary-dark);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.dropdown-notifications-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-light);
  transition: background-color var(--transition-fast) ease;
}

.dropdown-notifications-item:hover {
  background-color: var(--gray-50);
}

.dropdown-notifications-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.dropdown-notifications-content {
  flex: 1;
}

.dropdown-notifications-message {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  margin-bottom: var(--spacing-1);
}

.dropdown-notifications-time {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.dropdown-notifications-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.dropdown-notifications-link {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast) ease;
}

.dropdown-notifications-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 576px) {
  .dropdown-menu {
    position: fixed;
    top: auto;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    max-height: 80vh;
    margin: 0;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    transform: translateY(100%);
    overflow-y: auto;
  }

  .dropdown.show .dropdown-menu {
    transform: translateY(0);
  }

  .dropdown-menu-right,
  .dropdown-menu-center {
    left: 0;
    right: 0;
    transform: translateY(100%);
  }

  .dropdown.show .dropdown-menu-center {
    transform: translateY(0);
  }

  .dropup .dropdown-menu,
  .dropright .dropdown-menu,
  .dropleft .dropdown-menu {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateY(100%);
  }

  .dropup.show .dropdown-menu,
  .dropright.show .dropdown-menu,
  .dropleft.show .dropdown-menu {
    transform: translateY(0);
  }
}

/* User Dropdown Header - Separate sections for better mobile organization */
.user-dropdown-header {
  margin-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-light);
  padding-bottom: var(--spacing-2);
}

.user-dropdown-header:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Dropdown Section Title */
.dropdown-section-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: var(--spacing-2) var(--spacing-4);
  margin: 0 0 var(--spacing-1) 0;
  background-color: var(--gray-25);
}

/* Mobile-specific styles for better organization */
@media (max-width: 768px) {
  .user-dropdown-header {
    margin-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: var(--spacing-3);
  }

  .dropdown-section-title {
    font-size: var(--font-size-sm);
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--primary-color);
    color: var(--white);
    margin-bottom: var(--spacing-2);
    border-radius: var(--radius-sm);
  }

  /* Specific section styling for mobile */
  .profile-menu-section .dropdown-section-title {
    background-color: #007bff;
  }

  .password-menu-section {
    border-left: 3px solid #28a745;
  }

  .activity-menu-section {
    border-left: 3px solid #17a2b8;
  }

  .settings-menu-section .dropdown-section-title {
    background-color: #6c757d;
  }

  .account-menu-section .dropdown-section-title {
    background-color: #dc3545;
  }
}
