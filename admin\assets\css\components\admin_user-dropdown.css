/**
 * Admin User Dropdown CSS
 *
 * This file contains styles for the user dropdown menu in the admin panel.
 * Redesigned for a more modern and user-friendly experience.
 */

/* User Menu Container */
.topbar-user-menu {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  margin-left: 8px;
}

/* User Menu Toggle Button */
.user-menu-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast) ease;
  height: 40px;
}

.user-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-menu-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(241, 202, 47, 0.25);
}

/* User Avatar */
.user-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--background-light);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
  text-transform: uppercase;
}

.avatar-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--white);
}

.avatar-status.online {
  background-color: #4CAF50;
}

.avatar-status.away {
  background-color: #FFC107;
}

.avatar-status.offline {
  background-color: #9E9E9E;
}

.user-menu-toggle i {
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.user-menu-toggle .dropdown-icon {
  font-size: var(--font-size-xs);
  transition: transform var(--transition-fast) ease;
  margin-left: auto;
  color: var(--text-light);
}

.user-menu-toggle.active .dropdown-icon {
  transform: rotate(180deg);
  color: var(--primary-color);
}

/* User Name in Toggle */
.user-name {
  font-weight: var(--font-weight-medium);
  display: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  margin-right: var(--spacing-2);
}

@media (min-width: 768px) {
  .user-name {
    display: inline-block;
  }
}

@media (min-width: 992px) {
  .user-name {
    max-width: 150px;
  }
}

/* User Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 10px;
  z-index: 9999 !important; /* Higher z-index to ensure it's above everything */
  width: 320px;
  /* Remove max-height and overflow to allow natural height extension */
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  display: none;
  transform: translateY(10px);
  opacity: 0;
  transition: transform var(--transition-fast) ease, opacity var(--transition-fast) ease;
  overflow: visible; /* Remove scrollbars */
  margin-top: 5px; /* Add margin to ensure it's below the header */
}

.user-dropdown.show {
  display: block !important;
  transform: translateY(0) !important;
  opacity: 1;
  animation: dropdownFadeIn 0.2s ease-out;
  z-index: 9999 !important;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* User Dropdown Header */
.user-dropdown-header {
  padding: var(--spacing-4);
  display: flex !important;
  align-items: center;
  gap: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-light);
  margin-bottom: 0; /* Remove any margin that might cause gaps */
  visibility: visible !important;
  opacity: 1 !important;
  position: relative;
  z-index: 1;
}

.user-dropdown-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  border: 3px solid var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.avatar-img-large {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  text-transform: uppercase;
}

.user-dropdown-info {
  flex: 1;
  min-width: 0;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-name {
  margin: 0 0 4px 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-email {
  margin: 0 0 8px 0;
  font-size: var(--font-size-xs);
  color: var(--text-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.user-dropdown-badge {
  display: inline-block !important;
  padding: 2px 8px;
  background-color: var(--primary-color);
  color: var(--text-dark);
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dropdown Menu */
.dropdown-menu {
  padding: 0; /* Remove padding that causes gaps */
  margin: 0;
  display: block !important; /* Force display */
  visibility: visible !important; /* Force visibility */
  opacity: 1 !important; /* Force opacity */
  position: relative; /* Ensure proper positioning */
  z-index: 1; /* Ensure it's above other elements */
}

/* Ensure all dropdown items are visible */
.dropdown-menu .dropdown-item {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dropdown-menu .dropdown-link {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dropdown Section Title */
.dropdown-section-title {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  color: var(--text-muted) !important; /* Ensure proper muted text color */
  letter-spacing: 0.5px;
  margin-top: var(--spacing-2);
  display: none !important; /* Hidden by default on desktop */
  visibility: hidden !important; /* Hidden on desktop */
}

/* Dropdown Section */
.dropdown-section {
  margin-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-3);
}

.dropdown-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.dropdown-section-title:first-child {
  margin-top: 0;
}

/* Dropdown Item */
.dropdown-item {
  margin: 0;
  padding: 0;
  display: block !important; /* Force display */
  visibility: visible !important; /* Force visibility */
  opacity: 1 !important; /* Force opacity */
  position: relative; /* Ensure proper positioning */
}

.dropdown-link {
  padding: var(--spacing-3) var(--spacing-4);
  display: flex !important; /* Force display */
  align-items: center;
  gap: var(--spacing-3);
  color: var(--text-dark) !important; /* Ensure proper text color */
  text-decoration: none;
  transition: all var(--transition-fast) ease;
  font-size: var(--font-size-sm);
  border-left: 3px solid transparent;
  visibility: visible !important; /* Force visibility */
}

.dropdown-link:hover {
  background-color: var(--background-light);
  color: var(--primary-color) !important;
  border-left-color: var(--primary-color);
}

/* Special styling for logout link */
.dropdown-link.logout-link {
  color: var(--danger-color) !important;
  border-left-color: transparent;
}

.dropdown-link.logout-link:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-dark) !important;
  border-left-color: var(--danger-color);
}

.dropdown-link.logout-link .dropdown-icon {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

.dropdown-link.logout-link:hover .dropdown-icon {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--danger-dark);
}

.dropdown-link.logout-link .dropdown-link-description {
  color: rgba(220, 53, 69, 0.7) !important;
}

.dropdown-link.logout-link:hover .dropdown-link-description {
  color: var(--danger-dark) !important;
}

button.dropdown-link {
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: var(--font-size-base);
  background-color: var(--background-light);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.dropdown-link:hover .dropdown-icon {
  color: var(--primary-color);
  background-color: rgba(241, 202, 47, 0.1);
}

.dropdown-link-content {
  flex: 1;
  min-width: 0;
}

.dropdown-link-title {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: 2px;
  color: inherit; /* Inherit color from parent link */
}

.dropdown-link-description {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-muted) !important; /* Ensure proper muted text color */
}

/* Toggle Switch for Dark Mode */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  flex-shrink: 0;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-label {
  background-color: var(--primary-color);
}

.toggle-input:checked + .toggle-label:before {
  transform: translateX(20px);
}

/* Dropdown Footer */
.dropdown-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.dropdown-footer-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--text-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) 0;
  transition: color var(--transition-fast) ease;
}

.dropdown-footer-link:hover {
  color: var(--primary-color);
}

.dropdown-footer-link i {
  font-size: var(--font-size-base);
}

.dropdown-footer-info {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  text-align: center;
}

/* Dark Mode Styles */
.dark-mode .user-dropdown {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-light);
}

.dark-mode .user-dropdown-header {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .user-dropdown-name {
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .user-dropdown-email {
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-link {
  color: rgba(255, 255, 255, 0.8);
}

.dark-mode .dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .dropdown-icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-link-description {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .dropdown-section-title {
  color: rgba(255, 255, 255, 0.6);
}

.dark-mode .dropdown-footer {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .dropdown-footer-info {
  color: rgba(255, 255, 255, 0.5);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .user-dropdown {
    position: fixed !important;
    top: calc(var(--topbar-height) + 10px) !important; /* Position just below the topbar */
    right: 10px !important;
    width: 300px !important;
    max-width: calc(100vw - 20px) !important;
    max-height: calc(100vh - var(--topbar-height) - 20px) !important; /* Ensure it doesn't exceed viewport */
    overflow-y: auto !important; /* Allow scrolling if content is too tall */
    box-shadow: var(--shadow-xl) !important;
    z-index: 9999 !important;
    transform: none !important;
    margin: 0 !important; /* Remove all margins */
    padding: 0 !important; /* Remove all padding */
  }

  .user-dropdown.show {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
    z-index: 9999 !important;
  }

  /* Ensure dropdown menu content is fully visible */
  .user-dropdown .dropdown-menu {
    max-height: none !important;
    overflow: visible !important;
    padding: 0 !important; /* Remove any padding that causes gaps */
    margin: 0 !important; /* Remove any margin that causes gaps */
    border: none !important; /* Remove any borders */
  }

  /* Fix dropdown header in mobile - remove gaps */
  .user-dropdown-header {
    padding: 16px !important;
    margin: 0 !important;
    border-bottom: 1px solid var(--border-color) !important;
    background-color: var(--white) !important;
  }

  /* Show section titles on mobile */
  .user-dropdown .dropdown-section-title {
    display: block !important;
    visibility: visible !important;
    padding: 12px 16px 8px 16px !important;
    margin: 0 !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    color: var(--text-muted) !important;
    background-color: var(--gray-50) !important;
    border: none !important;
    line-height: 1 !important;
  }

  /* Specific section styling for mobile */
  .profile-menu-section .dropdown-section-title {
    background-color: #007bff !important;
    color: var(--white) !important;
  }

  .password-menu-section .dropdown-section-title {
    background-color: #28a745 !important;
    color: var(--white) !important;
  }

  .activity-menu-section .dropdown-section-title {
    background-color: #17a2b8 !important;
    color: var(--white) !important;
  }

  .settings-menu-section .dropdown-section-title {
    background-color: #6c757d !important;
    color: var(--white) !important;
  }

  .account-menu-section .dropdown-section-title {
    background-color: #dc3545 !important;
    color: var(--white) !important;
  }

  /* Fix dropdown items spacing in mobile - completely remove gaps */
  .user-dropdown .dropdown-item {
    min-height: auto !important;
    padding: 0 !important; /* Remove padding from container */
    margin: 0 !important; /* Remove any margin */
    border: none !important; /* Remove all borders */
    line-height: 1 !important; /* Reset line height */
    box-sizing: border-box !important;
    background-color: var(--white) !important;
  }

  /* Fix dropdown links spacing - this is where the actual content is */
  .user-dropdown .dropdown-link {
    padding: 14px 16px !important; /* Consistent padding only on the link */
    margin: 0 !important; /* Remove any margin */
    min-height: auto !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    line-height: 1.4 !important; /* Consistent line height */
    box-sizing: border-box !important;
    border: none !important; /* Remove any borders */
    width: 100% !important;
    text-decoration: none !important;
    color: var(--text-color) !important;
    background-color: transparent !important;
    transition: background-color 0.2s ease !important;
  }

  /* Hover effect for dropdown links */
  .user-dropdown .dropdown-link:hover {
    background-color: var(--gray-50) !important;
  }

  /* Fix dropdown sections - remove all spacing */
  .user-dropdown .dropdown-section {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  /* Fix dropdown footer - remove gaps */
  .user-dropdown .dropdown-footer {
    padding: 12px 16px !important;
    margin: 0 !important;
    border-top: 1px solid var(--border-color) !important;
    background-color: var(--gray-50) !important;
  }

  /* Fix toggle switch container in dark mode item */
  .user-dropdown .dropdown-item button.dropdown-link {
    justify-content: space-between !important;
    padding: 14px 16px !important;
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    font-family: inherit !important;
    font-size: inherit !important;
  }

  /* Fix toggle switch positioning */
  .user-dropdown .toggle-switch {
    margin-left: auto !important;
    flex-shrink: 0 !important;
  }

  /* Remove any extra spacing from dropdown content areas */
  .user-dropdown .dropdown-link-content {
    flex: 1 !important;
    min-width: 0 !important;
  }

  /* Fix dropdown icon spacing */
  .user-dropdown .dropdown-icon {
    flex-shrink: 0 !important;
    width: 16px !important;
    text-align: center !important;
  }

  .topbar-user-menu {
    margin-left: auto;
  }

  .user-menu-toggle {
    padding: var(--spacing-1) var(--spacing-2);
  }
}

@media (max-width: 576px) {
  .user-dropdown {
    width: 280px !important;
    right: 5px !important;
  }

  /* Override any conflicting styles for very small screens */
  .user-dropdown .dropdown-link {
    padding: 12px 14px !important; /* Consistent smaller padding */
    margin: 0 !important;
    min-height: auto !important;
    line-height: 1.4 !important;
    gap: 10px !important; /* Slightly smaller gap for small screens */
  }

  .user-dropdown-header {
    padding: 14px !important;
    margin: 0 !important;
  }

  .user-dropdown-avatar {
    width: 50px !important;
    height: 50px !important;
  }

  /* Ensure no gaps between items - keep container padding at 0 */
  .user-dropdown .dropdown-item {
    padding: 0 !important; /* Keep container padding at 0 */
    margin: 0 !important;
    border: none !important;
    line-height: 1 !important;
  }

  /* Fix section titles for small screens */
  .user-dropdown .dropdown-section-title {
    padding: 10px 14px 6px 14px !important;
    font-size: 10px !important;
  }

  /* Fix any remaining spacing issues */
  .user-dropdown .dropdown-menu {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
  }

  .user-dropdown .dropdown-section {
    padding: 0 !important;
    margin: 0 !important;
  }

  .user-dropdown .dropdown-footer {
    padding: 10px 14px !important;
    margin: 0 !important;
  }

  /* Fix toggle switch for small screens */
  .user-dropdown .dropdown-item button.dropdown-link {
    padding: 12px 14px !important;
  }
}
