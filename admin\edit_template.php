<?php
/**
 * Edit Email Template
 *
 * Dedicated page for editing email templates
 */

session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

// Include permissions class
require_once 'lib/Permissions.php';

// Initialize permission system
$permissions = new Permissions($conn, $_SESSION['user_id']);

// Check if user has permission to manage email templates
if (!$permissions->hasPermission('manage_email_templates') && !$permissions->hasPermission('edit_email_templates')) {
    $_SESSION['error_message'] = "You do not have permission to edit email templates.";
    header('Location: email_templates.php');
    exit;
}

// Include EmailTemplates class
require_once 'lib/EmailTemplates.php';

// Initialize EmailTemplates class
$emailTemplates = new EmailTemplates($conn);

// Set page title
$page_title = "Edit Email Template";

// Add CSS for the template editor
$extra_css = '
<link rel="stylesheet" href="assets/css/pages/template_editor.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/components/admin_editor.css?v=' . time() . '">
<link rel="stylesheet" href="assets/css/components/simple-editor.css?v=' . time() . '">
';

// Include header
require_once 'includes/header.php';

// Check if template ID is provided
$template_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$template = null;

if ($template_id > 0) {
    $template = $emailTemplates->getTemplateById($template_id);
}

if (!$template) {
    // No template found, redirect to settings page
    echo '<script>window.location.href = "settings.php#email-templates";</script>';
    exit;
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_template'])) {
    $subject = isset($_POST['subject']) ? $_POST['subject'] : '';
    $content = isset($_POST['content']) ? $_POST['content'] : '';
    $header_image = $template['header_image']; // Keep existing image by default

    // Handle header image upload
    if (isset($_FILES['header_image']) && $_FILES['header_image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../images/email-templates/';

        // Create directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_info = pathinfo($_FILES['header_image']['name']);
        $file_extension = strtolower($file_info['extension']);

        // Validate file type
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        if (in_array($file_extension, $allowed_types)) {
            // Generate unique filename
            $filename = 'template_' . $template_id . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $filename;

            if (move_uploaded_file($_FILES['header_image']['tmp_name'], $upload_path)) {
                // Delete old image if it exists
                if (!empty($template['header_image']) && file_exists('../' . $template['header_image'])) {
                    unlink('../' . $template['header_image']);
                }

                $header_image = 'images/email-templates/' . $filename;
            } else {
                $error_message = 'Failed to upload header image.';
            }
        } else {
            $error_message = 'Invalid file type. Please upload JPG, PNG, or GIF images only.';
        }
    }

    // Handle image removal
    if (isset($_POST['remove_header_image']) && $_POST['remove_header_image'] === '1') {
        // Delete existing image file
        if (!empty($template['header_image']) && file_exists('../' . $template['header_image'])) {
            unlink('../' . $template['header_image']);
        }
        $header_image = null;
    }

    // Validate inputs
    if (empty($subject)) {
        $error_message = 'Subject cannot be empty.';
    } elseif (empty($content)) {
        $error_message = 'Content cannot be empty.';
    } else {
        // Save template with header image
        $result = $emailTemplates->updateTemplateWithImage($template_id, $subject, $content, $header_image);

        if ($result) {
            // Log the action
            if ($conn->query("SHOW TABLES LIKE 'activity_log'")->num_rows > 0) {
                $user_id = $_SESSION['user_id'];
                $log_sql = "INSERT INTO activity_log (user_id, action, description, ip_address)
                            VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($log_sql);
                $action = 'update_template';
                $description = "Updated email template: " . $template['template_key'];
                $ip = $_SERVER['REMOTE_ADDR'];
                $stmt->bind_param("isss", $user_id, $action, $description, $ip);
                $stmt->execute();
            }

            $success_message = 'Template saved successfully!';

            // Refresh template data
            $template = $emailTemplates->getTemplateById($template_id);
        } else {
            $error_message = 'Failed to save template. Please try again.';
        }
    }
}

// Get all templates for navigation
$all_templates = $emailTemplates->getAllTemplates();

// Get company name and admin email from settings
$company_name = '';
$admin_email = '';
$company_name_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'company_name'";
$admin_email_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'admin_email'";

$company_name_result = $conn->query($company_name_query);
$admin_email_result = $conn->query($admin_email_query);

if ($company_name_result && $company_name_result->num_rows > 0) {
    $company_name = $company_name_result->fetch_assoc()['setting_value'];
}

if ($admin_email_result && $admin_email_result->num_rows > 0) {
    $admin_email = $admin_email_result->fetch_assoc()['setting_value'];
}

// Set page title and icon for enhanced header
$page_title = "Edit Email Template";
$page_icon = "fas fa-envelope";
$back_link = ['url' => 'settings.php?category=email#template-settings', 'text' => 'Back to Templates'];
?>

<div class="admin-container">
    <?php
    // Include the content header component
    include 'includes/content-header.php';
    ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="admin-content-body">
        <div class="admin-card">
            <div class="admin-card-header">
                <h2 class="admin-card-title">
                    <i class="fas fa-envelope"></i>
                    <?php echo htmlspecialchars($template['template_name']); ?>
                </h2>
                <div class="admin-card-subtitle">
                    Edit email template content and settings
                </div>
            </div>

            <div class="admin-card-body">
                <!-- Editor Tabs -->
                <div class="editor-tabs">
                    <button type="button" class="editor-tab active" data-tab="editor">
                        <i class="fas fa-code"></i> Editor
                    </button>
                    <button type="button" class="editor-tab" data-tab="preview">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>

                <!-- Editor Tab Content -->
                <div class="editor-tab-content active" id="editor-tab">
                    <form method="post" action="" id="template-form" class="admin-form" enctype="multipart/form-data">
                        <div class="admin-form-row">
                            <div class="admin-form-group">
                                <label for="subject" class="required">Subject Line</label>
                                <input type="text" id="subject" name="subject" class="form-control"
                                    value="<?php echo htmlspecialchars($template['subject']); ?>" required>
                                <div class="admin-form-hint">The subject line that will appear in the email</div>
                            </div>
                        </div>

                        <div class="admin-form-row">
                            <div class="admin-form-group">
                                <label for="header_image">Header Image</label>
                                <div class="header-image-section">
                                    <?php if (!empty($template['header_image'])): ?>
                                        <div class="current-header-image">
                                            <img src="<?php echo htmlspecialchars($template['header_image']); ?>" alt="Current Header Image" class="header-image-preview">
                                            <div class="header-image-actions">
                                                <button type="button" class="admin-btn danger small" onclick="removeHeaderImage()">
                                                    <i class="fas fa-trash"></i> Remove Image
                                                </button>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-header-image">
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-image"></i>
                                                <p>No header image set. Admin logo will be used by default.</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="header-image-upload">
                                        <input type="file" id="header_image" name="header_image" accept="image/*" class="form-control">
                                        <input type="hidden" id="remove_header_image" name="remove_header_image" value="0">
                                        <div class="admin-form-hint">Upload a custom header image for this email template. If no image is uploaded, the admin logo will be used by default. Recommended size: 600x200px</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-form-row">
                            <div class="admin-form-group">
                                <label for="content" class="required">Email Body</label>
                                <div class="variables-dropdown">
                                    <button type="button" class="variables-dropdown-btn admin-btn secondary">
                                        <i class="fas fa-code"></i> Insert Variable <i class="fas fa-caret-down"></i>
                                    </button>
                                    <div class="variables-dropdown-content">
                                        <div class="variables-list">
                                            <div class="variables-group">
                                                <div class="variables-group-title">System Variables</div>
                                                <span class="template-variable" data-variable="company_name">Company Name</span>
                                                <span class="template-variable" data-variable="site_url">Site URL</span>
                                                <span class="template-variable" data-variable="current_date">Current Date</span>
                                            </div>

                                            <div class="variables-group">
                                                <div class="variables-group-title">User Variables</div>
                                                <span class="template-variable" data-variable="username">Username</span>
                                                <span class="template-variable" data-variable="user_email">User Email</span>
                                                <span class="template-variable" data-variable="reset_link">Reset Link</span>
                                            </div>

                                            <div class="variables-group">
                                                <div class="variables-group-title">Contact Form Variables</div>
                                                <span class="template-variable" data-variable="contact_name">Contact Name</span>
                                                <span class="template-variable" data-variable="contact_email">Contact Email</span>
                                                <span class="template-variable" data-variable="contact_message">Contact Message</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <textarea id="content" name="content" class="wysiwyg-editor" required><?php echo htmlspecialchars($template['content']); ?></textarea>
                                <div class="admin-form-hint">HTML content of the email. You can use variables from the dropdown above.</div>
                            </div>
                        </div>

                        <div class="admin-form-row">
                            <div class="admin-form-actions">
                                <button type="submit" name="save_template" class="admin-btn primary">
                                    <i class="fas fa-save"></i> Save Template
                                </button>
                                <a href="settings.php?category=email#template-settings" class="admin-btn secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Preview Tab Content -->
                <div class="editor-tab-content" id="preview-tab">
                    <div class="email-preview-container">
                        <div class="email-preview-header">
                            <div class="email-preview-subject" id="preview-subject">
                                <?php echo htmlspecialchars($template['subject']); ?>
                            </div>
                            <div class="email-preview-from">
                                From: <strong><?php echo htmlspecialchars($company_name); ?></strong> &lt;<?php echo htmlspecialchars($admin_email); ?>&gt;
                            </div>
                            <div class="email-preview-to">
                                To: <strong>John Doe</strong> &lt;<EMAIL>&gt;
                            </div>
                        </div>
                        <div class="email-preview-body" id="preview-content">
                            <?php echo nl2br(htmlspecialchars($template['content'])); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>

<style>
/* Template specific styles */
.template-type-badge {
    display: inline-block;
    padding: 4px 8px;
    background-color: #f1ca2f;
    color: #333;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

/* Variables styling */
.variables-group {
    margin-bottom: 15px;
}

.variables-group-title {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.template-variable {
    display: block;
    padding: 6px 10px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.template-variable:hover {
    background-color: #f1ca2f;
    color: #333;
}

.code-editor {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.mt-3 {
    margin-top: 15px;
}

.mt-4 {
    margin-top: 20px;
}

/* Editor Tabs */
.editor-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.editor-tab {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 600;
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
}

.editor-tab:hover {
    color: #333;
    border-bottom-color: #ddd;
}

.editor-tab.active {
    color: #f1ca2f;
    border-bottom-color: #f1ca2f;
}

.editor-tab-content {
    display: none;
}

.editor-tab-content.active {
    display: block;
}

/* Editor Mode Toggle */
.editor-mode-toggle {
    display: flex;
    margin-right: auto;
}

.editor-mode-btn {
    padding: 6px 12px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.editor-mode-btn:first-child {
    border-radius: 4px 0 0 4px;
}

.editor-mode-btn:last-child {
    border-radius: 0 4px 4px 0;
}

.editor-mode-btn.active {
    background-color: #f1ca2f;
    color: #333;
    border-color: #f1ca2f;
}

/* Editor Container */
.editor-container {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    min-height: 300px;
}

.html-editor,
.visual-editor {
    display: none;
}

.html-editor.active,
.visual-editor.active {
    display: block;
}

.html-editor textarea {
    width: 100%;
    min-height: 300px;
    border: none;
    padding: 15px;
    font-family: monospace;
    resize: vertical;
}

.visual-content-area {
    min-height: 300px;
    padding: 15px;
    overflow-y: auto;
    background-color: #fff;
}

/* Email Preview Styles */
.email-preview-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.email-preview-header {
    background-color: #f5f5f5;
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.email-preview-subject {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
}

.email-preview-from,
.email-preview-to {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.email-preview-body {
    padding: 20px;
    background-color: #fff;
    min-height: 200px;
}

/* Email Editor Toolbar */
.email-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.variables-dropdown {
    position: relative;
}

.variables-dropdown-btn {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.variables-dropdown-btn:hover {
    background-color: #f1f1f1;
}

.variables-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background-color: #fff;
    min-width: 300px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
    padding: 10px;
}

.variables-dropdown-content.show {
    display: block;
}

/* Header Image Styles */
.header-image-section {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background-color: #f9f9f9;
}

.current-header-image {
    margin-bottom: 15px;
}

.header-image-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: block;
    margin-bottom: 10px;
}

.header-image-actions {
    display: flex;
    gap: 10px;
}

.no-header-image {
    margin-bottom: 15px;
}

.no-image-placeholder {
    text-align: center;
    padding: 40px 20px;
    border: 2px dashed #ddd;
    border-radius: 6px;
    color: #666;
}

.no-image-placeholder i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 10px;
    display: block;
}

.no-image-placeholder p {
    margin: 0;
    font-size: 14px;
}

.header-image-upload {
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.admin-btn.small {
    padding: 6px 12px;
    font-size: 12px;
}

.admin-btn.danger {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.admin-btn.danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.variables-search {
    display: flex;
    margin-bottom: 10px;
    position: relative;
}

.variables-search input {
    flex: 1;
    padding: 8px 30px 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.variables-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    display: none;
}

.variables-categories {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.variable-category {
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
}

.variable-category.active {
    background-color: #f1ca2f;
    border-color: #f1ca2f;
    color: #333;
}

.variables-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
    max-height: 200px;
    overflow-y: auto;
}

.template-variable {
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

.template-variable:hover {
    background-color: #f1f1f1;
}

.template-variable.clicked {
    background-color: #f1ca2f;
}

/* Form Styling */
.admin-form textarea {
    min-height: 300px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .variables-grid {
        grid-template-columns: 1fr;
    }

    .variables-dropdown-content {
        min-width: 250px;
        right: -50px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize editor
    initEditor();

    // Initialize template variables
    initVariablesDropdown();

    // Initialize preview
    initPreview();

    // Initialize tabs
    initTabs();

    // Auto-dismiss alerts after 5 seconds
    initAlertDismissal();
});

/**
 * Initialize the editor
 */
function initEditor() {
    const contentTextarea = document.getElementById('content');
    if (!contentTextarea) return;

    // Initialize the enhanced editor
    window.templateEditor = new EnhancedEditor('#content', {
        height: 400,
        placeholder: 'Enter email template content here...',
        autosave: false, // Disable autosave for templates
        showWordCount: true,
        allowCodeView: true,
        allowFullscreen: true
    });

    // Handle editor changes to update preview
    window.templateEditor.container.addEventListener('input', updatePreview);

    // Set initial preview
    updatePreview();
}

/**
 * Initialize alert auto-dismissal
 */
function initAlertDismissal() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 500);
        }, 5000);
    });
}

/**
 * Initialize variables dropdown
 */
function initVariablesDropdown() {
    // Toggle dropdown
    const dropdownBtn = document.querySelector('.variables-dropdown-btn');
    const dropdownContent = document.querySelector('.variables-dropdown-content');

    if (dropdownBtn && dropdownContent) {
        // Toggle dropdown on button click
        dropdownBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdownContent.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.variables-dropdown')) {
                dropdownContent.classList.remove('show');
            }
        });

        // Prevent dropdown from closing when clicking inside
        dropdownContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Setup variables click
    const variables = document.querySelectorAll('.template-variable');
    variables.forEach(variable => {
        variable.addEventListener('click', function(e) {
            // Prevent default action to avoid page refresh
            e.preventDefault();

            // Get variable text
            const variableText = this.getAttribute('data-variable');

            // Format variable
            const formattedVariable = '{' + variableText + '}';

            // Insert variable
            insertVariable(formattedVariable);

            // Add visual feedback
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 300);

            // Close dropdown after selecting a variable
            if (dropdownContent) {
                dropdownContent.classList.remove('show');
            }
        });
    });
}

/**
 * Insert a variable into the editor
 */
function insertVariable(variable) {
    if (window.templateEditor) {
        // Use the enhanced editor's insert functionality
        if (window.templateEditor.isCodeView) {
            // Insert into code view
            const textarea = window.templateEditor.codeEditor;
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;

            textarea.value = textarea.value.substring(0, startPos) +
                            variable +
                            textarea.value.substring(endPos);

            textarea.selectionStart = startPos + variable.length;
            textarea.selectionEnd = startPos + variable.length;
            textarea.focus();

            // Update the visual editor and original textarea
            window.templateEditor.updateFromCodeEditor();
        } else {
            // Insert into visual view
            window.templateEditor.visualEditor.focus();
            document.execCommand('insertText', false, variable);
            window.templateEditor.updateTextarea();
        }
    } else {
        // Fallback to original textarea
        const originalTextarea = document.getElementById('content');
        if (originalTextarea) {
            const startPos = originalTextarea.selectionStart;
            const endPos = originalTextarea.selectionEnd;

            originalTextarea.value = originalTextarea.value.substring(0, startPos) +
                                    variable +
                                    originalTextarea.value.substring(endPos);

            originalTextarea.selectionStart = startPos + variable.length;
            originalTextarea.selectionEnd = startPos + variable.length;
            originalTextarea.focus();
        }
    }

    // Update preview
    updatePreview();
}

/**
 * Initialize preview
 */
function initPreview() {
    // Set initial preview
    updatePreview();

    // Update preview when subject changes
    const subjectInput = document.getElementById('subject');
    if (subjectInput) {
        subjectInput.addEventListener('input', updatePreview);
    }
}

/**
 * Initialize tabs
 */
function initTabs() {
    const tabButtons = document.querySelectorAll('.editor-tab');
    const tabContents = document.querySelectorAll('.editor-tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding content
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Update preview when switching to preview tab
            if (targetTab === 'preview') {
                updatePreview();
            }
        });
    });
}

/**
 * Update the preview
 */
function updatePreview() {
    const subjectInput = document.getElementById('subject');
    const previewSubject = document.getElementById('preview-subject');
    const previewContent = document.getElementById('preview-content');

    // Update subject
    if (subjectInput && previewSubject) {
        previewSubject.textContent = subjectInput.value || 'No subject';
    }

    // Get content from editor
    let content = '';
    const contentTextarea = document.getElementById('content');

    if (contentTextarea) {
        content = contentTextarea.value;

        // Replace variables with sample values for preview
        content = content.replace(/{company_name}/g, 'Your Company');
        content = content.replace(/{site_url}/g, 'https://example.com');
        content = content.replace(/{username}/g, 'John Doe');
        content = content.replace(/{user_email}/g, '<EMAIL>');
        content = content.replace(/{contact_name}/g, 'Jane Smith');
        content = content.replace(/{contact_email}/g, '<EMAIL>');
        content = content.replace(/{contact_message}/g, 'This is a sample message from the contact form.');
        content = content.replace(/{reset_link}/g, 'https://example.com/reset-password?token=sample');
    }

    // Update preview content
    if (previewContent) {
        // Normalize line breaks first (convert \r\n and \r to \n)
        content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        // Then convert \n to <br> for HTML display
        previewContent.innerHTML = content.replace(/\n/g, '<br>');
    }
}

/**
 * Remove header image
 */
function removeHeaderImage() {
    if (confirm('Are you sure you want to remove the header image? The admin logo will be used by default.')) {
        // Set the hidden field to indicate image removal
        document.getElementById('remove_header_image').value = '1';

        // Hide the current image section
        const currentImageSection = document.querySelector('.current-header-image');
        if (currentImageSection) {
            currentImageSection.style.display = 'none';
        }

        // Show the no-image placeholder
        const noImageSection = document.querySelector('.no-header-image');
        if (noImageSection) {
            noImageSection.style.display = 'block';
        } else {
            // Create and show the no-image placeholder
            const headerImageSection = document.querySelector('.header-image-section');
            const noImageDiv = document.createElement('div');
            noImageDiv.className = 'no-header-image';
            noImageDiv.innerHTML = `
                <div class="no-image-placeholder">
                    <i class="fas fa-image"></i>
                    <p>No header image set. Admin logo will be used by default.</p>
                </div>
            `;
            headerImageSection.insertBefore(noImageDiv, headerImageSection.querySelector('.header-image-upload'));
        }

        // Clear the file input
        const fileInput = document.getElementById('header_image');
        if (fileInput) {
            fileInput.value = '';
        }
    }
}


</script>

<!-- Include our enhanced WYSIWYG editor -->
<script src="js/enhanced-editor.js?v=<?php echo time(); ?>"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Move the variables dropdown to the enhanced editor toolbar
    setTimeout(function() {
        const variablesDropdown = document.querySelector('.variables-dropdown');
        const editorToolbar = document.querySelector('.enhanced-editor-toolbar .toolbar-right');

        if (variablesDropdown && editorToolbar) {
            // Clone the dropdown and insert it into the toolbar
            const clonedDropdown = variablesDropdown.cloneNode(true);
            editorToolbar.insertBefore(clonedDropdown, editorToolbar.firstChild);

            // Remove the original dropdown
            variablesDropdown.remove();

            // Re-initialize the dropdown functionality for the cloned element
            initVariablesDropdown();
        }
    }, 100); // Small delay to ensure editor is initialized
});
</script>

<?php include 'includes/footer.php'; ?>
