/* Submenu Styles */
.page-submenu {
    background-color: #f0f0f0; /* Light grey for desktop */
    width: 100%;
    left: 0;
    right: 0;
    z-index: 100;
}

.submenu-toggle {
    display: none;
    background-color: #4a4a52;
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.submenu-toggle:after {
    content: '▼';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
}

.submenu-toggle.active:after {
    content: '▲';
}

.submenu-container {
    display: block;
    width: 100%;
    margin: 0;
    padding: 0;
}

.submenu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: center;
}

.submenu li {
    margin: 0 10px;
}

.submenu a {
    display: inline-block;
    color: #fff;
    text-decoration: none;
    font-weight: 600;
    padding: 12px 15px;
    transition: all 0.3s;
    background-color: #3c3c45;
    border-radius: 4px;
}

.submenu a:hover {
    background-color: #4a4a52;
    color: #f1ca2f;
}

.submenu a.active {
    background-color: #f1ca2f;
    color: #333;
}

/* Submenu toggle button (hidden by default) */
.submenu-toggle {
    display: none;
    width: 100%;
    padding: 15px 20px;
    background-color: #4a4a52;
    color: #fff;
    border: none;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
    margin: 0;
    outline: none;
}

.submenu-toggle:hover {
    background-color: #5a5a62;
}

.submenu-toggle:focus {
    outline: none;
    box-shadow: none;
}

.submenu-toggle.active {
    background-color: #f1ca2f;
    color: #333;
}

.submenu-arrow {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    font-size: 12px;
}

.submenu-toggle.active .submenu-arrow {
    transform: translateY(-50%) rotate(180deg);
}

/* Adjust main content */
.page-content {
    padding-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-submenu {
        padding: 0 !important;
        margin: 0 !important;
        position: relative;
        width: 100vw !important;
        max-width: 100vw !important;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        overflow-x: hidden;
        box-sizing: border-box;
        display: block !important;
        z-index: 100;
        background-color: #4a4a52; /* Dark background for mobile */
    }

    /* Show submenu toggle on mobile */
    .submenu-toggle {
        display: block !important;
        width: 100%;
        box-sizing: border-box;
    }

    .page-submenu .container {
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Hide submenu container by default on mobile */
    .submenu-container {
        display: none;
        width: 100%;
        left: 0;
        right: 0;
        margin: 0;
        padding: 0;
    }

    /* Show submenu container when active */
    .submenu-container.active {
        display: block;
    }

    .submenu {
        flex-direction: column;
        width: 100%;
        margin: 0;
        padding: 0;
    }

    .submenu li {
        margin: 0;
        width: 100%;
    }

    .submenu a {
        font-size: 14px;
        padding: 12px 15px;
        width: 100%;
        display: block;
        text-align: center;
        border-radius: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
        background-color: #4a4a52;
    }

    .submenu a:hover {
        background-color: #5a5a62;
    }

    /* General mobile responsiveness */
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Override container padding specifically for page-submenu */
    .page-submenu .container {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    .page-content {
        padding-top: 30px;
    }

    h1 {
        font-size: 28px !important;
    }

    h2 {
        font-size: 24px !important;
    }

    .content-image img {
        width: 100%;
        height: auto;
    }
}
