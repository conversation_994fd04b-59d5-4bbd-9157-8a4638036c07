/**
 * TinyMCE HTML Editor
 * Replaces CodeMirror with TinyMCE for better visual editing
 */

// Global variables
let tinymceEditor = null;
let currentMode = 'code';
let isModified = false;
let siteBaseUrl = '';
let originalFileContent = ''; // Store original content for reconstruction

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Use the global currentFilePath variable set by the main page
    // If not available, try to get from URL parameters as fallback
    if (typeof window.currentFilePath === 'undefined' || !window.currentFilePath) {
        const urlParams = new URLSearchParams(window.location.search);
        window.currentFilePath = urlParams.get('file') || '';
    }

    // Detect site base URL from meta tag (preferred) or fallback to path detection
    const baseUrlMeta = document.querySelector('meta[name="site-base-url"]');
    if (baseUrlMeta && baseUrlMeta.getAttribute('content')) {
        siteBaseUrl = baseUrlMeta.getAttribute('content').replace(/\/$/, ''); // Remove trailing slash
        console.log('Site base URL from meta tag:', siteBaseUrl);
    } else {
        // Fallback to path detection
        const currentPath = window.location.pathname;
        const adminIndex = currentPath.indexOf('/admin/');
        if (adminIndex !== -1) {
            // Get the site path (e.g., /manageinc or empty for document root)
            const sitePath = currentPath.substring(0, adminIndex);
            siteBaseUrl = window.location.origin + sitePath;
        } else {
            // Final fallback: assume document root
            siteBaseUrl = window.location.origin;
        }
        console.log('Site base URL from path detection:', siteBaseUrl);
    }

    console.log('=== TinyMCE HTML Editor Initialization ===');
    console.log('Site Base URL detected:', siteBaseUrl);
    console.log('Current file path:', window.currentFilePath);
    console.log('Full URL:', window.location.href);
    console.log('URL Params:', Object.fromEntries(new URLSearchParams(window.location.search)));

    function initializeEditor() {
        const textarea = document.getElementById('tinymce-editor');
        if (textarea) {
            // Store original content for reconstruction
            originalFileContent = textarea.value;
            console.log('Original file content stored (length: ' + originalFileContent.length + ')');

            initTinyMCE();
            initModeToggle();

            // Initialize version history functionality
            setTimeout(() => {
                if (typeof initVersionHistory === 'function') {
                    initVersionHistory();
                    console.log('Version history initialized');
                }
                if (typeof loadVersionHistory === 'function') {
                    loadVersionHistory();
                    console.log('Version history loaded');
                }
            }, 1500);
        }
    }

    // Check if admin header is loaded, if not wait for it
    if (document.body.classList.contains('admin-header-loaded')) {
        initializeEditor();
    } else {
        // Listen for the admin header loaded event
        document.addEventListener('adminHeaderLoaded', initializeEditor);
        // Fallback: initialize after a delay if event doesn't fire
        setTimeout(initializeEditor, 1000);
    }
});

function initTinyMCE() {
    // Use CSS files extracted from the original HTML content (passed from PHP)
    const cssFiles = window.extractedCssFiles || [];
    console.log('=== TINYMCE INITIALIZATION ===');
    console.log('Extracted CSS files:', cssFiles);
    console.log('CSS files type:', typeof cssFiles);
    console.log('CSS files length:', cssFiles.length);

    // Calculate dynamic height based on content
    const originalContent = document.getElementById('tinymce-editor').value;
    const contentHeight = calculateContentHeight(originalContent);

    // Prepare CSS for TinyMCE
    const preparedCss = prepareCssFilesForTinyMCE(cssFiles);
    const inlineStyles = buildInlineStyles(cssFiles);

    console.log('Prepared CSS for TinyMCE:', preparedCss);
    console.log('Inline styles for TinyMCE:', inlineStyles);

    tinymce.init({
        selector: '#tinymce-editor',
        height: contentHeight,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | fullscreen | help',

        // Load CSS files from the HTML content for proper styling
        content_css: preparedCss,
        content_style: inlineStyles,

        // Auto-resize to content
        autoresize_min_height: 400,
        autoresize_max_height: 2000,
        autoresize_bottom_margin: 50,

        // Allow editing
        readonly: false,

        // Start in visual mode by default
        init_instance_callback: function(editor) {
            tinymceEditor = editor;

            console.log('=== TINYMCE EDITOR INITIALIZED ===');
            console.log('Editor instance:', editor);
            console.log('Editor iframe:', editor.getContainer());

            // Check if CSS files were loaded
            setTimeout(() => {
                const iframe = editor.getContainer().querySelector('iframe');
                if (iframe && iframe.contentDocument) {
                    const iframeDoc = iframe.contentDocument;
                    const stylesheets = iframeDoc.querySelectorAll('link[rel="stylesheet"]');
                    console.log('CSS files loaded in TinyMCE iframe:', stylesheets.length);
                    stylesheets.forEach((link, index) => {
                        console.log(`  CSS ${index}: ${link.href} (loaded: ${link.sheet ? 'YES' : 'NO'})`);
                    });

                    const styles = iframeDoc.querySelectorAll('style');
                    console.log('Inline styles in TinyMCE iframe:', styles.length);
                    styles.forEach((style, index) => {
                        console.log(`  Style ${index}: ${style.textContent.length} characters`);
                    });

                    // FALLBACK: If no CSS files were loaded, try to inject them manually
                    if (stylesheets.length === 0 && window.extractedCssFiles && window.extractedCssFiles.length > 0) {
                        console.log('No CSS files loaded, attempting manual injection...');
                        injectCssIntoTinyMCE(iframeDoc, window.extractedCssFiles);
                    }

                    // Fix image loading in mobile view
                    fixMobileImageDisplay(iframeDoc);
                } else {
                    console.log('Could not access TinyMCE iframe document');
                }
            }, 1000);

            // Start in visual mode
            setTimeout(() => {
                switchToVisualMode();
            }, 100);

            // Track changes - only initiate lock on actual user editing
            editor.on('input', function(e) {
                console.log('Input event detected, editor initialized:', editor.initialized);
                if (editor.initialized) {
                    handleContentChange();
                }

                // Auto-resize after content changes
                setTimeout(() => {
                    autoResizeEditor();
                }, 100);

                // Fix mobile image display after content changes
                if (window.innerWidth <= 768) {
                    setTimeout(() => {
                        const iframeDoc = editor.getDoc();
                        if (iframeDoc) {
                            fixMobileImageDisplay(iframeDoc);
                        }
                    }, 200);
                }
            });

            // Also track keydown for immediate response
            editor.on('keydown', function(e) {
                console.log('Keydown event detected:', e.key);
                if (editor.initialized && e.key !== 'Tab' && e.key !== 'Shift' && e.key !== 'Control' && e.key !== 'Alt') {
                    handleContentChange();
                }
            });

            // Track paste and cut operations
            editor.on('paste cut', function(e) {
                console.log('Paste/Cut event detected');
                if (editor.initialized) {
                    setTimeout(() => {
                        handleContentChange();
                    }, 50);
                }
            });

            // Track when editor is ready and content is fully loaded
            editor.on('init', function() {
                console.log('TinyMCE editor initialized');
                setTimeout(() => {
                    editor.initialized = true;
                    console.log('Editor marked as ready for editing');

                    // Add additional event listeners directly to the editor body
                    const editorBody = editor.getBody();
                    if (editorBody) {
                        editorBody.addEventListener('input', function(e) {
                            console.log('Direct body input event detected');
                            if (editor.initialized) {
                                handleContentChange();
                            }
                        });

                        editorBody.addEventListener('keypress', function(e) {
                            console.log('Direct body keypress event detected:', e.key);
                            if (editor.initialized) {
                                handleContentChange();
                            }
                        });

                        console.log('Direct event listeners added to editor body');
                    }
                }, 500);
            });

            // Convert paths and block header/footer when setting content for visual mode
            editor.on('BeforeSetContent', function(e) {
                if (currentMode === 'visual') {
                    e.content = convertPathsForDisplay(e.content);
                    e.content = blockHeaderFooterEditing(e.content);
                }
            });

            // Block header/footer editing after content is set
            editor.on('SetContent', function() {
                if (currentMode === 'visual') {
                    setTimeout(() => {
                        blockHeaderFooterElements();
                        autoResizeEditor();
                    }, 100);
                }
            });
        },

        // Disable URL conversion since we pre-process content on the server side
        convert_urls: false,
        relative_urls: false,
        remove_script_host: false,
        document_base_url: siteBaseUrl + '/'
    });
}

function initModeToggle() {
    // No mode toggle needed - visual mode only
    console.log('Visual-only mode initialized');
}

function switchToCodeMode() {
    if (!tinymceEditor) return;

    currentMode = 'code';

    // Update button states
    const codeModeBtn = document.getElementById('codeMode');
    const visualModeBtn = document.getElementById('visualMode');

    if (codeModeBtn) {
        codeModeBtn.classList.add('active');
    }
    if (visualModeBtn) {
        visualModeBtn.classList.remove('active');
    }

    // Get current content and convert paths back to relative for code editing
    let content = tinymceEditor.getContent();
    content = convertPathsForSaving(content);

    // Switch to code mode using TinyMCE's code plugin
    tinymceEditor.execCommand('mceCodeEditor');

    // Set the content in the code editor
    setTimeout(() => {
        const codeTextarea = document.querySelector('.tox-textarea');
        if (codeTextarea) {
            codeTextarea.value = content;

            // Add header/footer protection to code mode
            addCodeModeProtection(codeTextarea);
        }
    }, 100);
}

function switchToVisualMode() {
    if (!tinymceEditor) return;

    currentMode = 'visual';

    // Get content from the original textarea (full HTML)
    const originalTextarea = document.getElementById('tinymce-editor');
    let content = originalTextarea ? originalTextarea.value : tinymceEditor.getContent();

    // Convert paths to absolute for proper display
    content = convertPathsForDisplay(content);

    // Extract only body content for visual editing (protect header/footer)
    content = extractEditableContent(content);

    // Set content in visual mode (this is programmatic, not user editing)
    tinymceEditor.setContent(content);

    // Setup protection and auto-resize after content is loaded
    setTimeout(() => {
        blockHeaderFooterElements();
        autoResizeEditor();
        console.log('Visual mode content loaded');

        // Only set initialized if not already set by the init event
        if (!tinymceEditor.initialized) {
            tinymceEditor.initialized = true;
            console.log('Editor marked as ready for editing (fallback)');
        }
    }, 300);
}

function formatCode() {
    if (!tinymceEditor || typeof html_beautify === 'undefined') return;

    const content = tinymceEditor.getContent();
    const formatted = html_beautify(content, {
        indent_size: 2,
        wrap_line_length: 120,
        preserve_newlines: true
    });

    tinymceEditor.setContent(formatted);
}

function convertPathsForDisplay(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');

    console.log('Converting paths for display. Base URL:', baseUrl);

    // Convert ALL relative paths to absolute URLs for proper display
    content = content.replace(/\b(src|href)="(?!https?:\/\/|\/\/|data:|mailto:|tel:|#)([^"]+)"/gi, function(match, attribute, path) {
        // Skip if already converted
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        let newPath;
        if (path.startsWith('/')) {
            // Absolute path from root
            newPath = `${window.location.origin}${path}`;
        } else {
            // Relative path - make it relative to site root, not admin
            newPath = `${baseUrl}/${path}`;
        }

        console.log(`Path conversion: ${path} -> ${newPath}`);
        return `${attribute}="${newPath}"`;
    });

    // Also convert CSS background images
    content = content.replace(/background-image:\s*url\(["']?(?!https?:\/\/|\/\/|data:)([^"')]+)["']?\)/gi, function(match, path) {
        if (path.includes(baseUrl) || path.includes(window.location.origin)) {
            return match;
        }

        let newPath;
        if (path.startsWith('/')) {
            newPath = `${window.location.origin}${path}`;
        } else {
            newPath = `${baseUrl}/${path}`;
        }

        console.log(`CSS path conversion: ${path} -> ${newPath}`);
        return match.replace(path, newPath);
    });

    console.log('Path conversion completed');
    return content;
}

function convertPathsForSaving(content) {
    if (!siteBaseUrl || !content) return content;

    const baseUrl = siteBaseUrl.replace(/\/$/, '');
    const baseUrlEscaped = baseUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const originEscaped = window.location.origin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Convert absolute URLs back to relative paths
    content = content.replace(new RegExp(`\\b(src|href)="${baseUrlEscaped}/([^"]*)"`, 'gi'), '$1="$2"');
    content = content.replace(new RegExp(`\\b(src|href)="${originEscaped}/([^"]*)"`, 'gi'), '$1="/$2"');

    return content;
}

function handleContentChange() {
    console.log('handleContentChange called - Editor:', !!tinymceEditor, 'Initialized:', tinymceEditor?.initialized, 'IsModified:', isModified, 'CurrentFilePath:', window.currentFilePath);

    // Only proceed if editor is fully initialized and content is loaded
    if (!tinymceEditor || !tinymceEditor.initialized) {
        console.log('Editor not ready, skipping lock acquisition');
        return;
    }

    // Only acquire lock on first actual user edit
    if (!isModified && window.currentFilePath) {
        console.log('First user edit detected, acquiring file lock for:', window.currentFilePath);

        // First edit - acquire lock
        isModified = true;

        // Update lock status in UI
        updateLockStatus(true, null);

        // Show unlock button
        const unlockButton = document.getElementById('unlockFileBtn');
        if (unlockButton) {
            unlockButton.style.display = 'inline-block';
            console.log('Unlock button shown');
        } else {
            console.log('Unlock button not found');
        }

        // Show visual feedback that lock is being acquired
        showLockAcquisitionMessage();

        // Acquire lock on server
        if (typeof acquireLock === 'function') {
            console.log('Calling acquireLock function...');
            acquireLock(window.currentFilePath).then(success => {
                if (success) {
                    console.log('File lock acquired successfully');
                    showLockSuccessMessage();
                } else {
                    console.error('Failed to acquire lock on server');
                    showLockFailureMessage();
                }
            }).catch(error => {
                console.error('Error acquiring lock:', error);
                showLockFailureMessage();
            });
        } else {
            console.log('acquireLock function not available');
            // Fallback: just show the lock status without server call
            showLockSuccessMessage();
        }
    } else {
        console.log('Lock acquisition skipped - isModified:', isModified, 'currentFilePath:', window.currentFilePath);
    }
}

function updateLockStatus(isLocked, lockInfo) {
    const lockStatus = document.getElementById('lockStatus');
    if (lockStatus) {
        if (isLocked) {
            lockStatus.className = 'lock-status locked';
            lockStatus.innerHTML = '<i class="fas fa-lock"></i> File is locked for editing';
        } else {
            lockStatus.className = 'lock-status not-locked';
            lockStatus.innerHTML = '<i class="fas fa-unlock"></i> File is not locked';
        }
    }
}

// Save functionality is now handled in form submission event

// Function to manually inject CSS into TinyMCE iframe
function injectCssIntoTinyMCE(iframeDoc, cssFiles) {
    console.log('=== MANUAL CSS INJECTION ===');
    console.log('Injecting CSS files into TinyMCE iframe:', cssFiles);

    cssFiles.forEach((cssFile, index) => {
        if (typeof cssFile === 'string') {
            // External CSS file
            console.log(`Injecting external CSS ${index}: ${cssFile}`);
            const link = iframeDoc.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = cssFile;
            link.onload = () => {
                console.log(`✅ CSS file ${index} loaded successfully: ${cssFile}`);
            };
            link.onerror = () => {
                console.log(`❌ CSS file ${index} failed to load: ${cssFile}`);
            };
            iframeDoc.head.appendChild(link);
        } else if (typeof cssFile === 'object' && cssFile.inline) {
            // Inline CSS
            console.log(`Injecting inline CSS ${index}, length: ${cssFile.inline.length}`);
            const style = iframeDoc.createElement('style');
            style.type = 'text/css';
            style.textContent = cssFile.inline;
            iframeDoc.head.appendChild(style);
            console.log(`✅ Inline CSS ${index} injected successfully`);
        }
    });
}

// Form submission is now handled by the simple syncTinyMCEContent() function
// called via onclick on the save button

// Function to prepare CSS files for TinyMCE content_css option
function prepareCssFilesForTinyMCE(cssFiles) {
    console.log('=== PREPARING CSS FILES FOR TINYMCE ===');
    console.log('Input CSS files:', cssFiles);

    if (!cssFiles || cssFiles.length === 0) {
        console.log('No CSS files provided, returning false');
        return false;
    }

    // Filter out inline styles and return only external CSS files
    const externalCssFiles = cssFiles.filter(file => {
        const isString = typeof file === 'string';
        const hasInline = typeof file === 'object' && file.hasOwnProperty('inline');
        console.log('Processing CSS file:', file);
        console.log('  - Type:', typeof file);
        console.log('  - Is string:', isString);
        console.log('  - Has inline property:', hasInline);

        if (isString) {
            // Test if the URL is accessible
            console.log('  - Testing accessibility of:', file);
            // We'll return true here and let TinyMCE handle the loading
            return true;
        }

        return false;
    });

    console.log('External CSS files for TinyMCE:', externalCssFiles);
    console.log('Total external files:', externalCssFiles.length);

    if (externalCssFiles.length > 0) {
        console.log('Returning CSS files array:', externalCssFiles);
        return externalCssFiles;
    } else {
        console.log('No external CSS files found, returning false');
        return false;
    }
}

// Function to build inline styles for TinyMCE content_style option
function buildInlineStyles(cssFiles) {
    console.log('=== BUILDING INLINE STYLES FOR TINYMCE ===');
    console.log('Input CSS files for inline styles:', cssFiles);

    let inlineStyles = '';

    // Add default styles if no CSS files
    if (!cssFiles || cssFiles.length === 0) {
        const defaultStyles = 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; line-height:1.6; margin:20px; }';
        console.log('No CSS files, using default styles:', defaultStyles);
        return defaultStyles;
    }

    // Extract inline styles from the CSS files array
    cssFiles.forEach((file, index) => {
        console.log(`Processing file ${index}:`, file);
        if (typeof file === 'object' && file.hasOwnProperty('inline')) {
            inlineStyles += file.inline + '\n';
            console.log(`Added inline styles from file ${index}, length:`, file.inline.length);
        }
    });

    // Add basic body styles if no inline styles found
    if (!inlineStyles.trim()) {
        inlineStyles = 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; line-height:1.6; margin:20px; }';
        console.log('No inline styles found, using default body styles');
    }

    console.log('Final inline styles for TinyMCE:', inlineStyles.substring(0, 200) + '...');
    return inlineStyles;
}

// Legacy CSS extraction function (kept for backward compatibility)
function extractCSSFiles(content) {
    const cssFiles = [];
    const cssRegex = /<link[^>]*rel=["']stylesheet["'][^>]*href=["']([^"']+)["'][^>]*>/gi;
    let match;

    while ((match = cssRegex.exec(content)) !== null) {
        let cssPath = match[1];

        // Convert relative paths to absolute URLs
        if (!cssPath.startsWith('http') && !cssPath.startsWith('//')) {
            if (cssPath.startsWith('/')) {
                cssPath = window.location.origin + cssPath;
            } else {
                const baseUrl = siteBaseUrl.replace(/\/$/, '');
                cssPath = baseUrl + '/' + cssPath;
            }
        }

        cssFiles.push(cssPath);
    }

    console.log('Extracted CSS files:', cssFiles);
    return cssFiles;
}

// Fix image display in mobile view
function fixMobileImageDisplay(iframeDoc) {
    if (!iframeDoc) return;

    console.log('Fixing mobile image display...');

    // Add mobile-specific styles to the iframe
    const mobileStyles = `
        <style>
        @media (max-width: 768px) {
            img {
                max-width: 100% !important;
                height: auto !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                margin: 10px 0 !important;
            }

            body {
                margin: 0 !important;
                padding: 10px !important;
                overflow-x: auto !important;
                word-wrap: break-word !important;
            }

            /* Ensure all content is visible */
            * {
                max-width: 100% !important;
                box-sizing: border-box !important;
            }
        }
        </style>
    `;

    // Inject mobile styles into iframe head
    const head = iframeDoc.head || iframeDoc.getElementsByTagName('head')[0];
    if (head) {
        head.insertAdjacentHTML('beforeend', mobileStyles);
        console.log('Mobile styles injected into iframe');
    }

    // Fix existing images
    const images = iframeDoc.querySelectorAll('img');
    images.forEach(img => {
        // Ensure images have proper error handling
        img.addEventListener('error', function() {
            console.log('Image failed to load in mobile view:', this.src);

            // Try to fix relative paths
            if (!this.src.startsWith('http')) {
                const baseUrl = window.location.origin;
                const newSrc = baseUrl + '/' + this.src.replace(/^\//, '');
                console.log('Trying with absolute path:', newSrc);
                this.src = newSrc;
            }
        });

        // Force image to be visible
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.display = 'block';
        img.style.visibility = 'visible';
        img.style.opacity = '1';
    });

    console.log(`Fixed ${images.length} images for mobile display`);
}

// Height calculation function
function calculateContentHeight(content) {
    // Create a temporary div to measure content height
    const tempDiv = document.createElement('div');
    tempDiv.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 800px;
        font-family: Helvetica, Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        padding: 20px;
        visibility: hidden;
    `;

    // Extract body content for measurement
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    const bodyContent = bodyMatch ? bodyMatch[1] : content;

    tempDiv.innerHTML = bodyContent;
    document.body.appendChild(tempDiv);

    // Get the height and add some padding
    const contentHeight = tempDiv.scrollHeight + 100; // Add 100px padding

    // Remove temporary div
    document.body.removeChild(tempDiv);

    // Set minimum and maximum heights
    const minHeight = 400;
    const maxHeight = 1500;

    const finalHeight = Math.max(minHeight, Math.min(maxHeight, contentHeight));

    console.log('Calculated editor height:', finalHeight);
    return finalHeight;
}

// Auto-resize function
function autoResizeEditor() {
    if (!tinymceEditor) return;

    try {
        const editorBody = tinymceEditor.getBody();
        if (!editorBody) return;

        // Get the content height
        const contentHeight = editorBody.scrollHeight;
        const currentHeight = parseInt(tinymceEditor.getContainer().style.height) || 600;

        // Calculate new height with padding
        const newHeight = Math.max(400, Math.min(1500, contentHeight + 100));

        // Only resize if there's a significant difference
        if (Math.abs(newHeight - currentHeight) > 50) {
            tinymceEditor.theme.resizeTo(null, newHeight);
            console.log('Auto-resized editor to:', newHeight);
        }
    } catch (error) {
        console.log('Auto-resize error:', error);
    }
}

// Header/Footer protection functions
function extractEditableContent(content) {
    // Extract only the body content for visual editing
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        return bodyMatch[1];
    }

    // If no body tags, remove head section and return the rest
    let editableContent = content.replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '');
    editableContent = editableContent.replace(/<\/?html[^>]*>/gi, '');
    editableContent = editableContent.replace(/<\/?body[^>]*>/gi, '');

    return editableContent;
}

function blockHeaderFooterEditing(content) {
    // Completely remove header and footer sections from visual editing
    // They will be preserved in the original content and restored on save
    content = content.replace(/<header[^>]*>[\s\S]*?<\/header>/gi, '');
    content = content.replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    return content;
}

function blockHeaderFooterElements() {
    // Additional protection: make any remaining header/footer elements completely uneditable
    const editor = tinymceEditor;
    if (!editor) return;

    const editorBody = editor.getBody();
    if (!editorBody) return;

    // Find and block any header/footer elements that might have slipped through
    const headers = editorBody.querySelectorAll('header');
    const footers = editorBody.querySelectorAll('footer');

    [...headers, ...footers].forEach(element => {
        element.contentEditable = 'false';
        element.style.cssText = `
            pointer-events: none !important;
            user-select: none !important;
            opacity: 0.5 !important;
            background: #f0f0f0 !important;
            border: 2px dashed #ccc !important;
            position: relative !important;
        `;

        // Add overlay to completely block interaction
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(240, 240, 240, 0.8) !important;
            z-index: 1000 !important;
            pointer-events: all !important;
            cursor: not-allowed !important;
        `;
        overlay.title = 'Header/Footer sections cannot be edited';

        if (element.style.position !== 'relative') {
            element.style.position = 'relative';
        }
        element.appendChild(overlay);
    });
}

function addCodeModeProtection(textarea) {
    if (!textarea) return;

    // Block editing of header/footer sections in code mode
    let originalValue = textarea.value;
    let isBlocking = false;

    textarea.addEventListener('input', function(e) {
        if (isBlocking) return;

        const currentValue = textarea.value;
        const headerMatch = currentValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
        const footerMatch = currentValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);
        const originalHeaderMatch = originalValue.match(/<header[^>]*>[\s\S]*?<\/header>/gi);
        const originalFooterMatch = originalValue.match(/<footer[^>]*>[\s\S]*?<\/footer>/gi);

        // Check if header or footer content has been modified
        let headerChanged = false;
        let footerChanged = false;

        if (headerMatch && originalHeaderMatch) {
            headerChanged = headerMatch[0] !== originalHeaderMatch[0];
        } else if (headerMatch !== originalHeaderMatch) {
            headerChanged = true;
        }

        if (footerMatch && originalFooterMatch) {
            footerChanged = footerMatch[0] !== originalFooterMatch[0];
        } else if (footerMatch !== originalFooterMatch) {
            footerChanged = true;
        }

        if (headerChanged || footerChanged) {
            isBlocking = true;
            textarea.value = originalValue;

            // Show blocking message
            showBlockingMessage('Header and footer sections cannot be edited in code mode.');

            setTimeout(() => {
                isBlocking = false;
            }, 100);
        } else {
            originalValue = currentValue;
        }
    });
}

function showBlockingMessage(message) {
    // Remove existing message
    const existingMessage = document.querySelector('.blocking-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create blocking message
    const messageDiv = document.createElement('div');
    messageDiv.className = 'blocking-message';
    messageDiv.innerHTML = `<i class="fas fa-ban"></i> ${message}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #dc3545;
        color: white;
        padding: 15px 25px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-size: 14px;
        font-weight: bold;
    `;

    document.body.appendChild(messageDiv);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}

// Lock acquisition feedback functions
function showLockAcquisitionMessage() {
    showStatusMessage('Acquiring file lock...', 'info', 'fas fa-spinner fa-spin');
}

function showLockSuccessMessage() {
    showStatusMessage('File locked for editing', 'success', 'fas fa-lock');
}

function showLockFailureMessage() {
    showStatusMessage('Failed to acquire file lock', 'error', 'fas fa-exclamation-triangle');
}

function showStatusMessage(message, type, icon) {
    // Remove existing status message
    const existingMessage = document.querySelector('.status-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Determine colors based on type
    let backgroundColor, textColor;
    switch (type) {
        case 'success':
            backgroundColor = '#28a745';
            textColor = 'white';
            break;
        case 'error':
            backgroundColor = '#dc3545';
            textColor = 'white';
            break;
        case 'info':
        default:
            backgroundColor = '#17a2b8';
            textColor = 'white';
            break;
    }

    // Create status message
    const messageDiv = document.createElement('div');
    messageDiv.className = 'status-message';
    messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        min-width: 250px;
        text-align: center;
    `;

    document.body.appendChild(messageDiv);

    // Auto-remove after 3 seconds (except for spinner)
    if (type !== 'info') {
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }
}

function reconstructFullContent(bodyContent) {
    console.log('Reconstructing full content...');
    console.log('Body content length:', bodyContent.length);
    console.log('Original content length:', originalFileContent.length);

    if (!originalFileContent) {
        console.warn('No original content stored, returning body content as-is');
        return bodyContent;
    }

    // Replace only the body content
    const bodyMatch = originalFileContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch) {
        console.log('Found body tags, replacing body content');
        const reconstructed = originalFileContent.replace(bodyMatch[1], bodyContent);
        console.log('Reconstructed content length:', reconstructed.length);
        return reconstructed;
    }

    console.log('No body tags found, returning body content as-is');
    // If no body tags, return the body content as-is
    return bodyContent;
}

// Export functions for use by other scripts
window.tinymceHtmlEditor = {
    switchToCodeMode,
    switchToVisualMode,
    formatCode,
    convertPathsForDisplay,
    convertPathsForSaving,
    extractEditableContent,
    blockHeaderFooterEditing,
    reconstructFullContent
};
