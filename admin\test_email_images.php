<?php
/**
 * Test Email Image URLs
 * 
 * This script tests the email template processor to ensure images use correct absolute URLs
 */

// Include necessary files
require_once 'config.php';
require_once 'lib/EmailTemplateProcessor.php';

// Create EmailTemplateProcessor instance
$processor = new EmailTemplateProcessor($conn);

// Test content with various image types
$test_content = '
<h2>Email Image Test</h2>
<p>Testing different image URL formats:</p>

<h3>1. Admin Logo (should be converted)</h3>
<img src="admin/images/logo.png" alt="Admin Logo">

<h3>2. Relative Image (should be converted)</h3>
<img src="images/test-image.jpg" alt="Test Image">

<h3>3. Root-relative Image (should be converted)</h3>
<img src="/images/banner.png" alt="Banner">

<h3>4. Already Absolute URL (should remain unchanged)</h3>
<img src="https://example.com/image.jpg" alt="External Image">

<h3>5. Data URL (should remain unchanged)</h3>
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" alt="Data Image">
';

// Process the template
echo "<h1>Email Image URL Test</h1>";
echo "<h2>Original Content:</h2>";
echo "<pre>" . htmlspecialchars($test_content) . "</pre>";

$processed_content = $processor->processTemplate($test_content);

echo "<h2>Processed Email Content:</h2>";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
echo $processed_content;
echo "</div>";

echo "<h2>Processed Content Source:</h2>";
echo "<pre>" . htmlspecialchars($processed_content) . "</pre>";

// Test the image processing function directly
echo "<h2>Direct Image Processing Test:</h2>";
$direct_processed = $processor->processEmailImages($test_content);
echo "<pre>" . htmlspecialchars($direct_processed) . "</pre>";

// Test settings
echo "<h2>Email Processor Settings:</h2>";
$settings = $processor->getSettings();
echo "<pre>";
print_r($settings);
echo "</pre>";
?>
