<?php
/**
 * Email Template Processor
 *
 * This class processes email templates by wrapping them in a consistent HTML structure
 * and replacing template variables with actual values.
 */
class EmailTemplateProcessor {
    private $conn;
    private $settings;

    /**
     * Constructor
     *
     * @param object $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }

    /**
     * Load settings from database
     */
    private function loadSettings() {
        // Get settings from system_settings table
        $sql = "SELECT category, setting_key, setting_value FROM system_settings
                WHERE category IN ('general', 'email', 'admin_appearance', 'appearance')";
        $result = $this->conn->query($sql);

        $this->settings = [
            'site_name' => 'Manage Inc.',
            'site_url' => 'https://manageinc.com',
            'admin_logo_path' => '../admin/images/logo.png',
            'primary_color' => '#f1ca2f',
            'secondary_color' => '#2c3e50',  // Default secondary color matching the sidebar
            'from_email' => '<EMAIL>',
            'from_name' => 'Manage Inc.'
        ];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $key = $row['category'] . '_' . $row['setting_key'];
                $this->settings[$key] = $row['setting_value'];

                // Also store some common settings with simpler keys
                if ($row['setting_key'] === 'site_name' && $row['category'] === 'general') {
                    $this->settings['site_name'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'site_url' && $row['category'] === 'general') {
                    $this->settings['site_url'] = $row['setting_value'];
                }
                // Admin logo path - check both admin_appearance and general categories
                if (($row['setting_key'] === 'admin_logo_path' && $row['category'] === 'admin_appearance') ||
                    ($row['setting_key'] === 'admin_logo' && $row['category'] === 'general')) {
                    $this->settings['admin_logo_path'] = $row['setting_value'];
                    // Store the timestamp to ensure we get the latest version of the logo
                    $this->settings['logo_timestamp'] = time();
                }

                // Email settings
                if ($row['setting_key'] === 'from_email' && $row['category'] === 'email') {
                    $this->settings['from_email'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'from_name' && $row['category'] === 'email') {
                    $this->settings['from_name'] = $row['setting_value'];
                }

                // Theme colors - prioritize appearance category
                if ($row['setting_key'] === 'primary_color' && $row['category'] === 'appearance') {
                    $this->settings['primary_color'] = $row['setting_value'];
                }
                if ($row['setting_key'] === 'secondary_color' && $row['category'] === 'appearance') {
                    $this->settings['secondary_color'] = $row['setting_value'];
                }
            }
        }
    }

    /**
     * Process an email template
     *
     * @param string $content The email content
     * @param array $variables Variables to replace in the template
     * @param array|null $template Template data (optional, for header image)
     * @return string The processed email content with HTML wrapper
     */
    public function processTemplate($content, $variables = [], $template = null) {
        // Replace variables in content
        $content = $this->replaceVariables($content, $variables);

        // Process images to use absolute URLs for email delivery
        $content = $this->processEmailImages($content);

        // Convert newlines to <br> tags if content is not already HTML
        if (strpos($content, '<html>') === false && strpos($content, '<body>') === false) {
            $content = nl2br($content);
        }

        // If content already has HTML structure, extract the body content
        if (strpos($content, '<body>') !== false) {
            preg_match('/<body>(.*?)<\/body>/is', $content, $matches);
            if (isset($matches[1])) {
                $content = $matches[1];
            }
        }

        // Determine which image to use for the header
        $header_image_path = null;

        // Check if template has a custom header image
        if ($template && !empty($template['header_image'])) {
            $header_image_path = $template['header_image'];
        } else {
            // Fall back to admin logo
            $header_image_path = $this->settings['admin_logo_path'];
        }

        $site_url = $this->settings['site_url'];
        $site_name = $this->settings['site_name'];
        $logo_timestamp = isset($this->settings['logo_timestamp']) ? $this->settings['logo_timestamp'] : time();

        // Make sure header image path is absolute for email delivery
        if (strpos($header_image_path, 'http') !== 0) {
            // Get the proper site URL for email delivery
            $email_site_url = $this->getEmailSiteUrl();

            // Clean the image path and make it absolute
            $clean_image_path = ltrim($header_image_path, '/');

            // If image path starts with 'admin/', it's an admin logo
            if (strpos($clean_image_path, 'admin/') === 0) {
                // Remove 'admin/' prefix and use the main site URL
                $clean_image_path = substr($clean_image_path, 6); // Remove 'admin/'
                $header_image_path = $email_site_url . '/' . $clean_image_path . '?v=' . $logo_timestamp;
            } else {
                // Regular image path
                $header_image_path = $email_site_url . '/' . $clean_image_path . '?v=' . $logo_timestamp;
            }
        }

        // Get theme colors
        $primary_color = $this->settings['primary_color'];
        $secondary_color = $this->settings['secondary_color'];

        // Create HTML wrapper with logo
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($site_name) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background-color: #ffffff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 0;
            border-bottom: 1px solid #eee;
            width: 100%;
        }
        .logo-background {
            background-color: ' . $secondary_color . ';
            padding: 15px 0;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .content-wrapper {
            padding: 20px;
        }
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            text-align: center;
        }
        .logo {
            max-width: 200px;
            max-height: 50px;
            height: auto;
            display: inline-block;
            background-color: transparent;
            margin: 0 auto;
        }
        .content {
            padding: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eee;
            text-align: center;
        }
        a { color: ' . $primary_color . '; }
        h1, h2, h3 { color: ' . $secondary_color . '; }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: ' . $primary_color . ';
            color: ' . $secondary_color . ' !important;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        @media only screen and (max-width: 620px) {
            .container { width: 100% !important; }
            .content { padding: 10px !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-background">
                <div class="logo-container">
                    <img src="' . $header_image_path . '" alt="' . htmlspecialchars($site_name) . '" class="logo">
                </div>
            </div>
        </div>
        <div class="content-wrapper">
            <div class="content">
                ' . $content . '
            </div>
            <div class="footer">
                <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($site_name) . '. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * Get the current settings
     *
     * @return array The current settings
     */
    public function getSettings() {
        return $this->settings;
    }

    /**
     * Replace template variables with actual values
     *
     * @param string $content The template content
     * @param array $variables Variables to replace
     * @return string The content with variables replaced
     */
    private function replaceVariables($content, $variables) {
        // Add common variables
        $common_variables = [
            'company_name' => $this->settings['site_name'],
            'site_name' => $this->settings['site_name'],
            'site_url' => $this->settings['site_url'],
            'current_date' => date('F j, Y')
        ];

        // Merge with provided variables (provided variables take precedence)
        $variables = array_merge($common_variables, $variables);

        // Replace variables in {variable} format
        foreach ($variables as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        // Also replace variables in {{variable}} format (for compatibility)
        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return $content;
    }

    /**
     * Get the proper site URL for email delivery
     * This ensures emails use the correct absolute URLs that recipients can access
     *
     * @return string The site URL for email delivery
     */
    private function getEmailSiteUrl() {
        // First, try to use the configured site URL from settings
        if (!empty($this->settings['site_url'])) {
            $site_url = rtrim($this->settings['site_url'], '/');

            // If it's a valid URL, use it
            if (filter_var($site_url, FILTER_VALIDATE_URL)) {
                return $site_url;
            }
        }

        // Fallback: construct URL from server variables
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // For localhost development, try to determine the correct path
        if ($host === 'localhost' || strpos($host, 'localhost:') === 0) {
            // Try to get the base path from the current script
            $script_dir = dirname($_SERVER['SCRIPT_NAME']);

            // If we're in the admin directory, go up one level
            if (basename($script_dir) === 'admin') {
                $base_path = dirname($script_dir);
            } else {
                $base_path = $script_dir;
            }

            // Clean up the path
            $base_path = rtrim($base_path, '/');
            if ($base_path === '' || $base_path === '.') {
                $base_path = '';
            }

            return $protocol . '://' . $host . $base_path;
        }

        // For production, use the host without additional paths
        return $protocol . '://' . $host;
    }

    /**
     * Process all images in email content to use absolute URLs
     *
     * @param string $content The email content
     * @return string The content with absolute image URLs
     */
    public function processEmailImages($content) {
        $email_site_url = $this->getEmailSiteUrl();

        // Pattern to match img src attributes
        $pattern = '/(<img[^>]+src=")([^"]+)(")/i';

        return preg_replace_callback($pattern, function($matches) use ($email_site_url) {
            $full_tag_start = $matches[1];
            $src_url = $matches[2];
            $full_tag_end = $matches[3];

            // Skip if already absolute URL
            if (strpos($src_url, 'http') === 0 || strpos($src_url, '//') === 0) {
                return $matches[0];
            }

            // Skip data URLs
            if (strpos($src_url, 'data:') === 0) {
                return $matches[0];
            }

            // Convert relative URL to absolute
            $clean_url = ltrim($src_url, '/');

            // If URL starts with 'admin/', remove it as images should be in main site
            if (strpos($clean_url, 'admin/') === 0) {
                $clean_url = substr($clean_url, 6);
            }

            $absolute_url = $email_site_url . '/' . $clean_url;

            return $full_tag_start . $absolute_url . $full_tag_end;
        }, $content);
    }
}
?>
