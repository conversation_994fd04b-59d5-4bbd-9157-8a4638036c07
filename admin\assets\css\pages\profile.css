/**
 * Profile Page CSS
 *
 * Professional profile page styling that matches the admin panel design
 */

/* Profile Container - Half Width */
.profile-container {
    max-width: 600px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Profile Overview Card */
.profile-overview-card {
    margin-bottom: 0;
}

/* Profile Tabs Card */
.profile-tabs-card {
    margin-bottom: 0;
}

/* Profile Overview in Card Header */
.profile-overview {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 10px 0;
}

.profile-avatar-section {
    flex-shrink: 0;
}

.profile-avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.profile-avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
}

.profile-info-section {
    flex: 1;
}

.profile-display-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

.profile-display-email {
    font-size: 1rem;
    color: var(--text-muted);
    margin-bottom: 10px;
}

.profile-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Tab Content Styling */
.admin-tab-content {
    padding: 0;
}

.admin-tab-pane {
    display: none;
    padding: 30px;
}

.admin-tab-pane.active {
    display: block;
}

/* Form Section Styling */
.admin-form-section {
    margin-bottom: 30px;
}

.admin-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-section-title i {
    color: var(--primary-color);
}

/* Form Grid Layout */
.admin-form-grid {
    display: grid;
    gap: 20px;
}

.admin-form-grid-2 {
    grid-template-columns: 1fr 1fr;
}

/* Form Group Styling */
.admin-form-group {
    margin-bottom: 20px;
}

.admin-form-label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.admin-form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background-color: var(--card-bg);
}

.admin-form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(241, 202, 47, 0.2);
}

.admin-form-hint {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-top: 5px;
}

/* Form Actions */
.admin-form-actions {
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
}

.admin-btn-primary {
    background-color: var(--primary-color);
    color: var(--text-dark);
}

.admin-btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

/* Image Preview Styling */
#image-preview {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-light);
    text-align: center;
}

#preview-img {
    max-width: 150px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-container {
        max-width: 100%;
        margin: 0;
        padding: 0 10px;
    }

    .profile-overview {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .profile-badges {
        justify-content: center;
    }

    .admin-form-grid-2 {
        grid-template-columns: 1fr;
    }

    .admin-tab-pane {
        padding: 20px;
    }

    .admin-form-actions {
        text-align: center;
    }

    .admin-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .profile-container {
        padding: 0 5px;
    }

    .profile-avatar-img,
    .profile-avatar-placeholder {
        width: 60px;
        height: 60px;
    }

    .profile-avatar-placeholder {
        font-size: 24px;
    }

    .profile-display-name {
        font-size: 1.3rem;
    }

    .admin-section-title {
        font-size: 1.1rem;
    }

    .admin-tab-pane {
        padding: 15px;
    }
}

/* Desktop - Ensure proper centering */
@media (min-width: 769px) {
    .profile-container {
        max-width: 600px;
    }
}
