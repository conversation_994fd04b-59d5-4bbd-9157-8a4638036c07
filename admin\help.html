<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center | Manage Inc.</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #f1ca2f;
            --primary-dark: #e0b929;
            --secondary: #333;
            --text: #333;
            --text-light: #666;
            --bg: #f8f9fa;
            --bg-card: #fff;
            --border: #e9ecef;
            --shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            --radius: 6px;
            --transition: all 0.3s ease;
            --header-height: 60px;
            --sidebar-width: 260px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.5;
            color: var(--text);
            background-color: var(--bg);
            font-size: 14px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: var(--bg-card);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text);
            cursor: pointer;
            margin-right: 15px;
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .header-logo i {
            color: var(--primary);
            font-size: 20px;
        }

        .header-logo h1 {
            font-size: 18px;
            font-weight: 600;
            color: var(--secondary);
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: var(--radius);
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .btn-secondary {
            background: var(--bg);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-secondary:hover {
            background: var(--border);
        }

        /* Main Layout */
        .main {
            display: flex;
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-card);
            border-right: 1px solid var(--border);
            overflow-y: auto;
            position: fixed;
            height: calc(100vh - var(--header-height));
            left: 0;
            top: var(--header-height);
            transition: var(--transition);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
        }

        .sidebar-header h2 {
            font-size: 16px;
            font-weight: 600;
            color: var(--secondary);
        }

        .search-container {
            position: relative;
            margin: 15px 20px;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 35px;
            border: 1px solid var(--border);
            border-radius: var(--radius);
            font-size: 13px;
            background: var(--bg);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0 20px 8px;
            margin-top: 20px;
        }

        .nav-items {
            list-style: none;
            padding: 0;
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 20px;
            color: var(--text);
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: var(--transition);
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(241, 202, 47, 0.1);
            color: var(--primary-dark);
        }

        .nav-link i {
            width: 16px;
            text-align: center;
            color: var(--primary);
            font-size: 14px;
        }

        /* Content Area */
        .content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            flex: 1;
            max-width: calc(100% - var(--sidebar-width));
        }

        .content-header {
            margin-bottom: 20px;
        }

        .breadcrumbs {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .breadcrumbs a {
            color: var(--primary);
            text-decoration: none;
        }

        .separator {
            margin: 0 6px;
        }

        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--secondary);
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 14px;
            color: var(--text-light);
            line-height: 1.5;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Cards */
        .card {
            background: var(--bg-card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 16px 20px;
            background: rgba(241, 202, 47, 0.05);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
        }

        .card-title i {
            color: var(--primary);
            font-size: 16px;
        }

        .card-body {
            padding: 20px;
        }

        .doc-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .doc-content h3 {
            font-size: 16px;
            font-weight: 600;
            margin: 20px 0 10px;
            color: var(--secondary);
        }

        .doc-content p {
            margin-bottom: 12px;
        }

        .doc-content ol,
        .doc-content ul {
            margin-bottom: 12px;
            padding-left: 20px;
        }

        .doc-content li {
            margin-bottom: 6px;
        }

        .doc-content a {
            color: var(--primary);
            text-decoration: none;
        }

        .doc-content a:hover {
            text-decoration: underline;
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: var(--bg-card);
            border-radius: var(--radius);
            padding: 20px;
            border: 1px solid var(--border);
            transition: var(--transition);
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: var(--primary);
        }

        .feature-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(241, 202, 47, 0.1);
            color: var(--primary);
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 18px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--secondary);
        }

        .feature-desc {
            color: var(--text-light);
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .feature-link {
            color: var(--primary);
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .feature-link:hover {
            text-decoration: underline;
        }

        /* Step Lists */
        .step-list {
            counter-reset: step-counter;
            list-style-type: none;
            padding-left: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background-color: var(--primary);
            color: var(--secondary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
        }

        /* Info Boxes */
        .info-box {
            background: rgba(241, 202, 47, 0.05);
            border: 1px solid rgba(241, 202, 47, 0.2);
            border-radius: var(--radius);
            padding: 16px;
            margin: 16px 0;
        }

        .info-box-title {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .info-box-title i {
            color: var(--primary);
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .sidebar {
                left: -260px;
                z-index: 99;
            }

            .sidebar.active {
                left: 0;
            }

            .content {
                margin-left: 0;
            }

            .menu-toggle {
                display: block;
            }

            .overlay {
                display: none;
                position: fixed;
                top: var(--header-height);
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 98;
            }

            .overlay.active {
                display: block;
            }

            .feature-grid {
                grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 0 15px;
            }

            .content {
                padding: 15px;
            }

            .content-title {
                font-size: 20px;
            }

            .card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <button class="menu-toggle" id="menuToggle">
            <i class="fas fa-bars"></i>
        </button>
        <div class="header-logo">
            <i class="fas fa-question-circle"></i>
            <h1>Manage Inc Help Center</h1>
        </div>
        <div class="header-actions">
            <a href="../index.html" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Site</span>
            </a>
        </div>
    <!-- Main content -->
    <main class="main">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>Documentation</h2>
            </div>

            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search documentation..." id="searchInput">
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Getting Started</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#overview" class="nav-link active" data-tab="overview">
                            <i class="fas fa-home"></i>
                            <span>Overview</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#user-guide" class="nav-link" data-tab="user-guide">
                            <i class="fas fa-book"></i>
                            <span>User Guide</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Features</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#news" class="nav-link" data-tab="news">
                            <i class="fas fa-newspaper"></i>
                            <span>News Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#users" class="nav-link" data-tab="users">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#inbox" class="nav-link" data-tab="inbox">
                            <i class="fas fa-inbox"></i>
                            <span>Inbox</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#editor" class="nav-link" data-tab="editor">
                            <i class="fas fa-code"></i>
                            <span>Frontend Editor</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link" data-tab="settings">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3 class="nav-section-title">Resources</h3>
                <ul class="nav-items">
                    <li class="nav-item">
                        <a href="#tutorials" class="nav-link" data-tab="tutorials">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Tutorials</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#faq" class="nav-link" data-tab="faq">
                            <i class="fas fa-question"></i>
                            <span>FAQ</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Overlay for mobile -->
        <div class="overlay" id="overlay"></div>

        <!-- Content area -->
        <div class="content">
            <!-- Overview tab -->
            <div id="overview" class="tab-content active">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>Overview</span>
                    </div>
                    <h1 class="content-title">Manage Inc Admin Panel</h1>
                    <p class="content-subtitle">Welcome to the comprehensive documentation for the Manage Inc Admin Panel. This guide will help you understand and effectively use all features of the admin system.</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3 class="feature-title">News Management</h3>
                        <p class="feature-desc">Create, edit, and organize news posts and categories for your website.</p>
                        <a href="#news" class="feature-link" data-tab="news">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="feature-title">User Management</h3>
                        <p class="feature-desc">Manage user accounts, roles, and permissions for the admin panel.</p>
                        <a href="#users" class="feature-link" data-tab="users">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h3 class="feature-title">Inbox</h3>
                        <p class="feature-desc">View and respond to contact form submissions from your website visitors.</p>
                        <a href="#inbox" class="feature-link" data-tab="inbox">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="feature-title">Frontend Editor</h3>
                        <p class="feature-desc">Edit website content directly through a WYSIWYG or code editor.</p>
                        <a href="#editor" class="feature-link" data-tab="editor">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h3 class="feature-title">Settings</h3>
                        <p class="feature-desc">Configure system settings including email, appearance, and fonts.</p>
                        <a href="#settings" class="feature-link" data-tab="settings">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="feature-title">Tutorials</h3>
                        <p class="feature-desc">Step-by-step guides for common tasks in the admin panel.</p>
                        <a href="#tutorials" class="feature-link" data-tab="tutorials">
                            Learn more <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-lightbulb"></i>
                            How to Use This Documentation
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <ol>
                            <li><strong>New Users</strong>: Start with the <a href="#user-guide" data-tab="user-guide">User Guide</a> to understand the basic concepts and features</li>
                            <li><strong>Task-Oriented Users</strong>: Go directly to the <a href="#tutorials" data-tab="tutorials">Tutorials</a> for step-by-step instructions</li>
                            <li><strong>Reference Needs</strong>: Use the search function to find specific information quickly</li>
                            <li><strong>Optimization</strong>: Review the Best Practices sections to improve your workflow and content quality</li>
                        </ol>

                        <h3>Getting Help</h3>
                        <p>If you can't find the information you need in this documentation:</p>
                        <ol>
                            <li>Look for contextual help icons (?) throughout the admin interface</li>
                            <li>Check the <a href="#faq" data-tab="faq">FAQ section</a> for answers to common questions</li>
                            <li>Contact your system administrator for assistance with technical issues</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- User Guide tab -->
            <div id="user-guide" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>User Guide</span>
                    </div>
                    <h1 class="content-title">User Guide</h1>
                    <p class="content-subtitle">Learn how to use the admin panel effectively with step-by-step instructions.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-rocket"></i>
                            Getting Started
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <ol class="step-list">
                            <li>Log in to your admin panel using your credentials</li>
                            <li>Familiarize yourself with the dashboard and navigation menu</li>
                            <li>Start by creating your first news post or editing existing content</li>
                            <li>Configure your system settings according to your needs</li>
                            <li>Set up user accounts and permissions if needed</li>
                        </ol>

                        <div class="info-box">
                            <div class="info-box-title">
                                <i class="fas fa-lightbulb"></i>
                                Pro Tip
                            </div>
                            <p>Use the search function in the sidebar to quickly find specific documentation topics.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ tab -->
            <div id="faq" class="tab-content">
                <div class="content-header">
                    <div class="breadcrumbs">
                        <a href="#overview">Documentation</a>
                        <span class="separator">/</span>
                        <span>FAQ</span>
                    </div>
                    <h1 class="content-title">Frequently Asked Questions</h1>
                    <p class="content-subtitle">Common questions and answers about using the admin panel.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-question-circle"></i>
                            Common Questions
                        </h2>
                    </div>
                    <div class="card-body doc-content">
                        <h3>How do I reset my password?</h3>
                        <p>You can reset your password by going to your profile settings and clicking "Change Password".</p>

                        <h3>Can I edit content on mobile devices?</h3>
                        <p>Yes, the admin panel is fully responsive and works on all devices including tablets and smartphones.</p>

                        <h3>How do I backup my content?</h3>
                        <p>Contact your system administrator for backup procedures and data export options.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links and tabs
                    navLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(tab => tab.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Show corresponding tab content
                    const targetTab = this.getAttribute('data-tab');
                    const targetContent = document.getElementById(targetTab);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });

            // Feature link navigation
            const featureLinks = document.querySelectorAll('.feature-link[data-tab]');
            featureLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all nav links and tabs
                    navLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(tab => tab.classList.remove('active'));

                    // Find and activate the corresponding nav link
                    const targetTab = this.getAttribute('data-tab');
                    const targetNavLink = document.querySelector(`.nav-link[data-tab="${targetTab}"]`);
                    if (targetNavLink) {
                        targetNavLink.classList.add('active');
                    }

                    // Show corresponding tab content
                    const targetContent = document.getElementById(targetTab);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });

            // Mobile menu toggle
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                });
            }

            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const navItems = document.querySelectorAll('.nav-item');

                    navItems.forEach(item => {
                        const text = item.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>