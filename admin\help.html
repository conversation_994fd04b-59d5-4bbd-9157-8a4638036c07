<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Inc Documentation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #f1ca2f;
            --primary-dark: #e0b929;
            --text-color: #1a1a1a;
            --text-muted: #6b7280;
            --border-color: #e5e7eb;
            --bg-color: #ffffff;
            --bg-secondary: #f9fafb;
            --sidebar-width: 280px;
            --header-height: 64px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            font-size: 16px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: var(--bg-color);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 24px;
            z-index: 1000;
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            text-decoration: none;
        }

        .header-brand i {
            color: var(--primary-color);
            font-size: 24px;
        }

        .header-nav {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-link {
            color: var(--text-muted);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s;
        }

        .header-link:hover {
            color: var(--text-color);
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-color);
            cursor: pointer;
            margin-left: auto;
        }

        /* Main Layout */
        .container {
            display: flex;
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            position: fixed;
            height: calc(100vh - var(--header-height));
            left: 0;
            top: var(--header-height);
        }

        .sidebar-content {
            padding: 24px 0;
        }

        .sidebar-section {
            margin-bottom: 32px;
        }

        .sidebar-section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
            padding: 0 24px 12px;
            margin-bottom: 8px;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav-item {
            margin-bottom: 4px;
        }

        .sidebar-nav-link {
            display: block;
            padding: 8px 24px;
            color: var(--text-muted);
            text-decoration: none;
            font-size: 14px;
            line-height: 1.4;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .sidebar-nav-link:hover {
            color: var(--text-color);
            background-color: rgba(241, 202, 47, 0.05);
        }

        .sidebar-nav-link.active {
            color: var(--primary-color);
            background-color: rgba(241, 202, 47, 0.1);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 40px 48px;
            max-width: calc(100% - var(--sidebar-width));
        }

        .content-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 18px;
            color: var(--text-muted);
            line-height: 1.5;
        }

        /* Content Sections */
        .content-section {
            margin-bottom: 48px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 16px;
            line-height: 1.3;
        }

        .section-content {
            font-size: 16px;
            line-height: 1.7;
            color: var(--text-color);
        }

        .section-content p {
            margin-bottom: 16px;
        }

        .section-content h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            margin: 32px 0 16px;
            line-height: 1.3;
        }

        .section-content h4 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin: 24px 0 12px;
            line-height: 1.3;
        }

        .section-content ul,
        .section-content ol {
            margin-bottom: 16px;
            padding-left: 24px;
        }

        .section-content li {
            margin-bottom: 8px;
        }

        .section-content a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .section-content a:hover {
            text-decoration: underline;
        }

        .section-content strong {
            font-weight: 600;
        }

        .section-content code {
            background: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin: 32px 0;
        }

        .feature-card {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 24px;
            transition: all 0.2s;
        }

        .feature-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: rgba(241, 202, 47, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }

        .feature-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .feature-description {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .feature-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
        }

        .feature-link:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                padding: 32px 24px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .header-nav {
                display: none;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                z-index: 1001;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 24px 16px;
                max-width: 100%;
            }

            .page-title {
                font-size: 28px;
            }

            .page-subtitle {
                font-size: 16px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        /* Mobile Overlay */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: var(--header-height);
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .mobile-overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <a href="#" class="header-brand">
            <i class="fas fa-book"></i>
            Manage Inc Documentation
        </a>
        <nav class="header-nav">
            <a href="dashboard.php" class="header-link">Dashboard</a>
            <a href="../index.html" class="header-link">Back to Site</a>
        </nav>
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <!-- Main Container -->
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Introduction</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#what-is-manage-inc" class="sidebar-nav-link active">Introduction</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#getting-started" class="sidebar-nav-link">Getting Started</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#key-features" class="sidebar-nav-link">Key Features</a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Features</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#news-management" class="sidebar-nav-link">News Management</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#user-management" class="sidebar-nav-link">User Management</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#inbox" class="sidebar-nav-link">Inbox & Messages</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#frontend-editor" class="sidebar-nav-link">Frontend Editor</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#settings" class="sidebar-nav-link">System Settings</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#analytics" class="sidebar-nav-link">Analytics & Reports</a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Resources</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#tutorials" class="sidebar-nav-link">Tutorials</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#faq" class="sidebar-nav-link">FAQ</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#support" class="sidebar-nav-link">Support</a>
                        </li>
                    </ul>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">Introduction</h1>
                <p class="page-subtitle">Welcome to the Manage Inc Admin Panel documentation. This guide will help you understand and effectively use all features of the admin system.</p>
            </div>

            <div class="content-section" id="what-is-manage-inc">
                <h2 class="section-title">What is Manage Inc Admin Panel?</h2>
                <div class="section-content">
                    <p>Manage Inc Admin Panel is a comprehensive content management system that helps you manage your website content, users, and system settings efficiently. Think of it as the control center for your website, bringing everything together in one intuitive interface.</p>

                    <p>Most admin panels are complex, difficult to use, and require technical expertise. Manage Inc is different. It's built to be user-friendly, powerful, and flexible. You get enterprise-level functionality without the usual complications.</p>
                </div>
            </div>

            <div class="content-section" id="why-manage-inc">
                <h2 class="section-title">Why Manage Inc?</h2>
                <div class="section-content">
                    <p>Because managing your website should be simple and efficient. Your admin panel should empower you, not frustrate you.</p>

                    <p>With Manage Inc Admin Panel, you get a complete set of tools for all your content management needs, built-in and ready to use. With Manage Inc, you get:</p>

                    <ol>
                        <li><strong>Complete content management</strong> – From news articles to page content, everything you need is built-in.</li>
                        <li><strong>User-friendly interface</strong> – A clean, modern interface that doesn't require technical expertise.</li>
                        <li><strong>Powerful editor</strong> – Professional WYSIWYG editor with advanced formatting options.</li>
                        <li><strong>Responsive design</strong> – Works perfectly on desktop, tablet, and mobile devices.</li>
                    </ol>

                    <p>Whether you're managing a small website or a large content portal, Manage Inc helps you stay organized, streamline your workflow, and focus on what really matters - creating great content.</p>
                </div>
            </div>

            <div class="content-section" id="key-features">
                <h2 class="section-title">Key Features</h2>
                <div class="section-content">
                    <p>Manage Inc Admin Panel will help you to:</p>

                    <ol>
                        <li>Create and manage news articles and blog posts</li>
                        <li>Edit website content with a professional WYSIWYG editor</li>
                        <li>Manage user accounts and permissions</li>
                        <li>Handle contact form submissions through the inbox</li>
                        <li>Configure system settings and preferences</li>
                        <li>Track user activities with comprehensive logging</li>
                        <li>Upload and manage media files</li>
                        <li>Customize website appearance and layout</li>
                        <li>Monitor system performance and analytics</li>
                        <li>Backup and restore your content</li>
                        <li>Manage email templates and notifications</li>
                        <li>Control access with role-based permissions</li>
                    </ol>

                    <p>And much more.</p>
                </div>
            </div>

            <div class="content-section" id="getting-started">
                <h2 class="section-title">Getting Started</h2>
                <div class="section-content">
                    <p>Ready to start using Manage Inc Admin Panel? Here's how to get up and running quickly:</p>

                    <ol>
                        <li><strong>Login</strong>: Access your admin panel using your provided credentials</li>
                        <li><strong>Dashboard</strong>: Familiarize yourself with the dashboard and main navigation</li>
                        <li><strong>First Content</strong>: Create your first news post or edit existing content</li>
                        <li><strong>Settings</strong>: Configure your system settings and preferences</li>
                        <li><strong>Users</strong>: Set up additional user accounts if needed</li>
                    </ol>

                    <p>For detailed step-by-step instructions, check out our <a href="#tutorials">tutorials section</a>.</p>
                </div>
            </div>

            <div class="content-section" id="feature-overview">
                <h2 class="section-title">Feature Overview</h2>
                <div class="section-content">
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <h3 class="feature-title">News Management</h3>
                            <p class="feature-description">Create, edit, and organize news posts and articles with our intuitive content management system.</p>
                            <a href="#news-management" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="feature-title">User Management</h3>
                            <p class="feature-description">Manage user accounts, roles, and permissions with comprehensive user administration tools.</p>
                            <a href="#user-management" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h3 class="feature-title">Inbox & Messages</h3>
                            <p class="feature-description">Handle contact form submissions and manage communications in one centralized location.</p>
                            <a href="#inbox" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3 class="feature-title">Frontend Editor</h3>
                            <p class="feature-description">Edit website content directly with our professional WYSIWYG editor and code view options.</p>
                            <a href="#frontend-editor" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h3 class="feature-title">System Settings</h3>
                            <p class="feature-description">Configure system-wide settings, email preferences, and customize your admin experience.</p>
                            <a href="#settings" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="feature-title">Analytics & Reports</h3>
                            <p class="feature-description">Monitor system performance and user activities with comprehensive reporting tools.</p>
                            <a href="#analytics" class="feature-link">Learn more →</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News Management Section -->
            <div class="content-section" id="news-management">
                <h2 class="section-title">News Management</h2>
                <div class="section-content">
                    <p>The News Management system allows you to create, edit, and organize news articles and blog posts for your website. This powerful content management tool provides everything you need to maintain an active and engaging news section.</p>

                    <h3>Creating News Articles</h3>
                    <p>To create a new news article:</p>
                    <ol>
                        <li>Navigate to <strong>News → Create News</strong> from the main menu</li>
                        <li>Enter a compelling <strong>title</strong> for your article</li>
                        <li>Select appropriate <strong>categories</strong> to organize your content</li>
                        <li>Write your content using the <strong>WYSIWYG editor</strong></li>
                        <li>Add <strong>featured images</strong> to make your articles more engaging</li>
                        <li>Set <strong>publication date</strong> and status (Draft/Published)</li>
                        <li>Click <strong>Save</strong> to store your article</li>
                    </ol>

                    <h3>Managing Categories</h3>
                    <p>Categories help organize your news content and make it easier for visitors to find relevant articles:</p>
                    <ul>
                        <li><strong>Create Categories</strong>: Go to News → Categories to add new categories</li>
                        <li><strong>Organize Content</strong>: Assign articles to relevant categories</li>
                        <li><strong>Category Pages</strong>: Each category automatically gets its own page</li>
                        <li><strong>Navigation</strong>: Categories appear in your website's navigation menu</li>
                    </ul>

                    <h3>Content Editor Features</h3>
                    <p>The built-in editor provides professional formatting options:</p>
                    <ul>
                        <li><strong>Rich Text Formatting</strong>: Bold, italic, underline, colors, fonts</li>
                        <li><strong>Lists and Tables</strong>: Create organized content with lists and tables</li>
                        <li><strong>Images and Media</strong>: Insert images, videos, and other media</li>
                        <li><strong>Links</strong>: Add internal and external links</li>
                        <li><strong>Code View</strong>: Switch to HTML code view for advanced editing</li>
                        <li><strong>Auto-save</strong>: Your work is automatically saved as you type</li>
                    </ul>

                    <h3>Publishing Options</h3>
                    <p>Control when and how your content appears:</p>
                    <ul>
                        <li><strong>Draft Mode</strong>: Save articles as drafts for later publication</li>
                        <li><strong>Scheduled Publishing</strong>: Set future publication dates</li>
                        <li><strong>Featured Articles</strong>: Mark important articles as featured</li>
                        <li><strong>SEO Settings</strong>: Optimize articles for search engines</li>
                    </ul>
                </div>
            </div>

            <!-- User Management Section -->
            <div class="content-section" id="user-management">
                <h2 class="section-title">User Management</h2>
                <div class="section-content">
                    <p>The User Management system provides comprehensive tools for managing user accounts, roles, and permissions within the admin panel. Control who has access to what features and maintain security across your system.</p>

                    <h3>Creating User Accounts</h3>
                    <p>To add new users to your admin panel:</p>
                    <ol>
                        <li>Go to <strong>Users → Create User</strong> from the main menu</li>
                        <li>Enter the user's <strong>personal information</strong> (name, email, etc.)</li>
                        <li>Set a <strong>secure password</strong> or generate one automatically</li>
                        <li>Assign appropriate <strong>roles and permissions</strong></li>
                        <li>Upload a <strong>profile picture</strong> (optional)</li>
                        <li>Set account <strong>status</strong> (Active/Inactive)</li>
                        <li>Click <strong>Save</strong> to create the account</li>
                    </ol>

                    <h3>User Roles and Permissions</h3>
                    <p>The system includes several built-in roles with different permission levels:</p>
                    <ul>
                        <li><strong>Super Admin</strong>: Full access to all features and settings</li>
                        <li><strong>Admin</strong>: Access to most features except system settings</li>
                        <li><strong>Editor</strong>: Can create and edit content, limited admin access</li>
                        <li><strong>Author</strong>: Can create and edit their own content only</li>
                        <li><strong>Viewer</strong>: Read-only access to admin panel</li>
                    </ul>

                    <h3>Managing User Profiles</h3>
                    <p>Users can manage their own profiles and admins can edit any user:</p>
                    <ul>
                        <li><strong>Personal Information</strong>: Update name, email, contact details</li>
                        <li><strong>Password Changes</strong>: Secure password update system</li>
                        <li><strong>Profile Pictures</strong>: Upload and manage profile images</li>
                        <li><strong>Preferences</strong>: Set language, timezone, and interface preferences</li>
                        <li><strong>Activity Log</strong>: View user's recent activities and login history</li>
                    </ul>

                    <h3>Security Features</h3>
                    <p>Built-in security measures protect your admin panel:</p>
                    <ul>
                        <li><strong>Password Requirements</strong>: Enforce strong password policies</li>
                        <li><strong>Login Attempts</strong>: Track and limit failed login attempts</li>
                        <li><strong>Session Management</strong>: Automatic logout after inactivity</li>
                        <li><strong>Two-Factor Authentication</strong>: Optional 2FA for enhanced security</li>
                        <li><strong>IP Restrictions</strong>: Limit access from specific IP addresses</li>
                    </ul>
                </div>
            </div>

            <!-- Inbox & Messages Section -->
            <div class="content-section" id="inbox">
                <h2 class="section-title">Inbox & Messages</h2>
                <div class="section-content">
                    <p>The Inbox system centralizes all communication from your website visitors, including contact form submissions, inquiries, and feedback. Manage all your messages efficiently from one location.</p>

                    <h3>Viewing Messages</h3>
                    <p>Access and organize your messages effectively:</p>
                    <ol>
                        <li>Navigate to <strong>Inbox</strong> from the main menu</li>
                        <li>View <strong>message list</strong> with sender, subject, and date</li>
                        <li>Use <strong>filters</strong> to sort by read/unread, date, or sender</li>
                        <li>Click on any message to <strong>view full content</strong></li>
                        <li>Mark messages as <strong>read/unread</strong> for organization</li>
                        <li>Use <strong>search function</strong> to find specific messages</li>
                    </ol>

                    <h3>Message Management</h3>
                    <p>Organize and respond to messages efficiently:</p>
                    <ul>
                        <li><strong>Reply Directly</strong>: Respond to messages from within the admin panel</li>
                        <li><strong>Forward Messages</strong>: Send messages to other team members</li>
                        <li><strong>Archive Messages</strong>: Keep important messages for future reference</li>
                        <li><strong>Delete Messages</strong>: Remove spam or irrelevant messages</li>
                        <li><strong>Bulk Actions</strong>: Perform actions on multiple messages at once</li>
                    </ul>

                    <h3>Contact Form Integration</h3>
                    <p>All contact forms on your website automatically send messages to the inbox:</p>
                    <ul>
                        <li><strong>Automatic Collection</strong>: All form submissions appear in the inbox</li>
                        <li><strong>Form Details</strong>: View which form was used and when</li>
                        <li><strong>Visitor Information</strong>: See visitor's IP address and browser details</li>
                        <li><strong>Attachment Support</strong>: Handle file attachments from contact forms</li>
                    </ul>

                    <h3>Email Notifications</h3>
                    <p>Stay informed about new messages:</p>
                    <ul>
                        <li><strong>Instant Notifications</strong>: Get email alerts for new messages</li>
                        <li><strong>Daily Summaries</strong>: Receive daily digest of all messages</li>
                        <li><strong>Custom Alerts</strong>: Set up alerts for specific types of messages</li>
                        <li><strong>Mobile Notifications</strong>: Get notifications on your mobile device</li>
                    </ul>
                </div>
            </div>

            <!-- Frontend Editor Section -->
            <div class="content-section" id="frontend-editor">
                <h2 class="section-title">Frontend Editor</h2>
                <div class="section-content">
                    <p>The Frontend Editor allows you to edit your website content directly through a powerful WYSIWYG (What You See Is What You Get) editor. Make changes to your website pages without needing technical knowledge.</p>

                    <h3>Accessing the Editor</h3>
                    <p>Start editing your website content:</p>
                    <ol>
                        <li>Go to <strong>Frontend → Editor</strong> from the main menu</li>
                        <li>Select the <strong>page or file</strong> you want to edit</li>
                        <li>Choose between <strong>Visual Mode</strong> or <strong>Code Mode</strong></li>
                        <li>The editor will load with your current content</li>
                        <li>Make your changes using the editing tools</li>
                        <li>Click <strong>Save</strong> to apply changes to your website</li>
                    </ol>

                    <h3>Visual Editor Features</h3>
                    <p>The visual editor provides intuitive content editing:</p>
                    <ul>
                        <li><strong>WYSIWYG Interface</strong>: See exactly how your content will look</li>
                        <li><strong>Drag and Drop</strong>: Move elements around your page easily</li>
                        <li><strong>Rich Text Formatting</strong>: Bold, italic, colors, fonts, and more</li>
                        <li><strong>Image Management</strong>: Upload, resize, and position images</li>
                        <li><strong>Link Creation</strong>: Add internal and external links</li>
                        <li><strong>Table Editor</strong>: Create and edit tables with ease</li>
                        <li><strong>Media Embedding</strong>: Embed videos, audio, and other media</li>
                    </ul>

                    <h3>Code Editor Mode</h3>
                    <p>For advanced users who prefer working with code:</p>
                    <ul>
                        <li><strong>HTML Editing</strong>: Direct HTML code editing with syntax highlighting</li>
                        <li><strong>CSS Styling</strong>: Add custom styles to your content</li>
                        <li><strong>JavaScript Support</strong>: Add interactive elements to your pages</li>
                        <li><strong>Code Validation</strong>: Automatic error checking and suggestions</li>
                        <li><strong>Preview Mode</strong>: Switch between code and preview instantly</li>
                    </ul>

                    <h3>File Management</h3>
                    <p>Manage your website files efficiently:</p>
                    <ul>
                        <li><strong>File Browser</strong>: Navigate through your website's file structure</li>
                        <li><strong>Upload Files</strong>: Add new images, documents, and media files</li>
                        <li><strong>File Organization</strong>: Create folders and organize your files</li>
                        <li><strong>File Permissions</strong>: Control who can access and edit files</li>
                        <li><strong>Backup System</strong>: Automatic backups before making changes</li>
                    </ul>

                    <h3>Safety Features</h3>
                    <p>Built-in safety measures protect your website:</p>
                    <ul>
                        <li><strong>Auto-save</strong>: Changes are saved automatically as you work</li>
                        <li><strong>Version History</strong>: Restore previous versions if needed</li>
                        <li><strong>Preview Changes</strong>: See changes before publishing them live</li>
                        <li><strong>File Locking</strong>: Prevent conflicts when multiple users edit</li>
                        <li><strong>Rollback Feature</strong>: Quickly undo changes if something goes wrong</li>
                    </ul>
                </div>
            </div>

            <!-- System Settings Section -->
            <div class="content-section" id="settings">
                <h2 class="section-title">System Settings</h2>
                <div class="section-content">
                    <p>The System Settings section allows you to configure various aspects of your admin panel and website. Customize the system to match your needs and preferences.</p>

                    <h3>General Settings</h3>
                    <p>Configure basic system information and preferences:</p>
                    <ul>
                        <li><strong>Site Information</strong>: Set site name, description, and contact details</li>
                        <li><strong>Admin Logo</strong>: Upload a custom logo for the admin panel</li>
                        <li><strong>Timezone</strong>: Set the default timezone for your system</li>
                        <li><strong>Language</strong>: Choose the default language for the interface</li>
                        <li><strong>Date Format</strong>: Customize how dates are displayed</li>
                        <li><strong>Maintenance Mode</strong>: Enable maintenance mode when needed</li>
                    </ul>

                    <h3>Email Configuration</h3>
                    <p>Set up email settings for system notifications and communications:</p>
                    <ul>
                        <li><strong>SMTP Settings</strong>: Configure SMTP server for sending emails</li>
                        <li><strong>From Address</strong>: Set the default sender email address</li>
                        <li><strong>Email Templates</strong>: Customize email templates for notifications</li>
                        <li><strong>Notification Settings</strong>: Choose which events trigger email notifications</li>
                        <li><strong>Test Email</strong>: Send test emails to verify configuration</li>
                    </ul>

                    <h3>Security Settings</h3>
                    <p>Configure security options to protect your system:</p>
                    <ul>
                        <li><strong>Password Policy</strong>: Set minimum password requirements</li>
                        <li><strong>Session Timeout</strong>: Configure automatic logout timing</li>
                        <li><strong>Login Attempts</strong>: Set limits on failed login attempts</li>
                        <li><strong>IP Whitelist</strong>: Restrict access to specific IP addresses</li>
                        <li><strong>SSL Settings</strong>: Configure HTTPS and SSL options</li>
                    </ul>

                    <h3>Appearance Settings</h3>
                    <p>Customize the look and feel of your admin panel:</p>
                    <ul>
                        <li><strong>Theme Selection</strong>: Choose from available admin themes</li>
                        <li><strong>Color Scheme</strong>: Customize colors to match your brand</li>
                        <li><strong>Font Settings</strong>: Select fonts for the admin interface</li>
                        <li><strong>Layout Options</strong>: Configure sidebar and layout preferences</li>
                        <li><strong>Custom CSS</strong>: Add custom styles to the admin panel</li>
                    </ul>

                    <h3>System Information</h3>
                    <p>View important system information and diagnostics:</p>
                    <ul>
                        <li><strong>Server Information</strong>: View PHP version, server details</li>
                        <li><strong>Database Status</strong>: Check database connection and size</li>
                        <li><strong>File Permissions</strong>: Verify file and folder permissions</li>
                        <li><strong>System Health</strong>: Monitor system performance and issues</li>
                        <li><strong>Update Status</strong>: Check for available system updates</li>
                    </ul>
                </div>
            </div>

            <!-- Tutorials Section -->
            <div class="content-section" id="tutorials">
                <h2 class="section-title">Tutorials</h2>
                <div class="section-content">
                    <p>Step-by-step tutorials to help you master the Manage Inc Admin Panel. These guides will walk you through common tasks and advanced features.</p>

                    <h3>Getting Started Tutorials</h3>
                    <p>Essential tutorials for new users:</p>
                    <ol>
                        <li><strong>First Login and Dashboard Tour</strong>
                            <ul>
                                <li>How to log in to your admin panel</li>
                                <li>Understanding the dashboard layout</li>
                                <li>Navigating the main menu</li>
                                <li>Customizing your profile</li>
                            </ul>
                        </li>
                        <li><strong>Creating Your First News Article</strong>
                            <ul>
                                <li>Accessing the news creation page</li>
                                <li>Using the WYSIWYG editor</li>
                                <li>Adding images and media</li>
                                <li>Publishing and scheduling content</li>
                            </ul>
                        </li>
                        <li><strong>Managing Website Content</strong>
                            <ul>
                                <li>Using the frontend editor</li>
                                <li>Editing existing pages</li>
                                <li>Creating new pages</li>
                                <li>Managing file uploads</li>
                            </ul>
                        </li>
                    </ol>

                    <h3>User Management Tutorials</h3>
                    <p>Learn how to manage users and permissions:</p>
                    <ol>
                        <li><strong>Creating and Managing User Accounts</strong>
                            <ul>
                                <li>Adding new users to the system</li>
                                <li>Setting up user roles and permissions</li>
                                <li>Managing user profiles</li>
                                <li>Handling user account issues</li>
                            </ul>
                        </li>
                        <li><strong>Setting Up User Roles</strong>
                            <ul>
                                <li>Understanding different user roles</li>
                                <li>Creating custom roles</li>
                                <li>Assigning permissions</li>
                                <li>Testing role functionality</li>
                            </ul>
                        </li>
                    </ol>

                    <h3>Advanced Features Tutorials</h3>
                    <p>Master advanced functionality:</p>
                    <ol>
                        <li><strong>Email System Configuration</strong>
                            <ul>
                                <li>Setting up SMTP email</li>
                                <li>Configuring email templates</li>
                                <li>Testing email functionality</li>
                                <li>Troubleshooting email issues</li>
                            </ul>
                        </li>
                        <li><strong>System Backup and Maintenance</strong>
                            <ul>
                                <li>Creating system backups</li>
                                <li>Restoring from backups</li>
                                <li>Regular maintenance tasks</li>
                                <li>Monitoring system health</li>
                            </ul>
                        </li>
                        <li><strong>Customizing the Admin Interface</strong>
                            <ul>
                                <li>Changing themes and colors</li>
                                <li>Adding custom CSS</li>
                                <li>Configuring layout options</li>
                                <li>Creating a branded experience</li>
                            </ul>
                        </li>
                    </ol>

                    <h3>Troubleshooting Guides</h3>
                    <p>Solutions for common issues:</p>
                    <ul>
                        <li><strong>Login Problems</strong>: Resolving authentication issues</li>
                        <li><strong>File Upload Issues</strong>: Fixing file upload problems</li>
                        <li><strong>Email Not Working</strong>: Troubleshooting email configuration</li>
                        <li><strong>Performance Issues</strong>: Optimizing system performance</li>
                        <li><strong>Permission Errors</strong>: Fixing file and user permission issues</li>
                    </ul>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="content-section" id="faq">
                <h2 class="section-title">Frequently Asked Questions</h2>
                <div class="section-content">
                    <p>Find answers to the most commonly asked questions about the Manage Inc Admin Panel.</p>

                    <h3>General Questions</h3>
                    <h4>How do I reset my password?</h4>
                    <p>You can reset your password by going to your profile settings and clicking "Change Password". If you've forgotten your password completely, contact your system administrator for a password reset.</p>

                    <h4>Can I edit content on mobile devices?</h4>
                    <p>Yes, the admin panel is fully responsive and works on all devices including tablets and smartphones. However, for the best editing experience, we recommend using a desktop or laptop computer.</p>

                    <h4>How do I backup my content?</h4>
                    <p>Contact your system administrator for backup procedures and data export options. Regular backups are typically handled automatically by the system.</p>

                    <h4>What browsers are supported?</h4>
                    <p>The admin panel works with all modern browsers including Chrome, Firefox, Safari, and Edge. We recommend using the latest version of your preferred browser for the best experience.</p>

                    <h3>Content Management Questions</h3>
                    <h4>How do I add images to my articles?</h4>
                    <p>In the content editor, click the image icon in the toolbar, then either upload a new image or select from existing images in your media library. You can resize and position images after inserting them.</p>

                    <h4>Can I schedule articles to publish later?</h4>
                    <p>Yes, when creating or editing an article, you can set a future publication date. The article will automatically become visible on your website at the scheduled time.</p>

                    <h4>How do I create categories for my news articles?</h4>
                    <p>Go to News → Categories from the main menu. Click "Add New Category" and enter the category name and description. You can then assign articles to these categories when creating or editing them.</p>

                    <h4>What's the difference between Draft and Published status?</h4>
                    <p>Draft articles are saved but not visible on your website. Published articles are live and visible to your website visitors. You can switch between these statuses at any time.</p>

                    <h3>User Management Questions</h3>
                    <h4>What are the different user roles?</h4>
                    <p>The system includes several roles: Super Admin (full access), Admin (most features), Editor (content management), Author (own content only), and Viewer (read-only access).</p>

                    <h4>How do I give someone access to the admin panel?</h4>
                    <p>Go to Users → Create User, enter their information, set a password, and assign an appropriate role. They'll receive login credentials to access the admin panel.</p>

                    <h4>Can users change their own passwords?</h4>
                    <p>Yes, all users can change their own passwords by going to their profile settings. They'll need to enter their current password to confirm the change.</p>

                    <h3>Technical Questions</h3>
                    <h4>Why are my emails not being sent?</h4>
                    <p>Check your email settings in System Settings → Email Configuration. Ensure your SMTP settings are correct and test the configuration. Contact your hosting provider if issues persist.</p>

                    <h4>How do I enable maintenance mode?</h4>
                    <p>Go to System Settings → General Settings and enable "Maintenance Mode". This will show a maintenance message to visitors while allowing admin access.</p>

                    <h4>What file types can I upload?</h4>
                    <p>You can upload most common file types including images (JPG, PNG, GIF), documents (PDF, DOC, DOCX), and media files (MP4, MP3). File size limits depend on your server configuration.</p>

                    <h4>How do I customize the admin panel appearance?</h4>
                    <p>Go to System Settings → Appearance to change themes, colors, and fonts. You can also add custom CSS for advanced customization.</p>
                </div>
            </div>

            <!-- Support Section -->
            <div class="content-section" id="support">
                <h2 class="section-title">Learning and Support</h2>
                <div class="section-content">
                    <p>Get help and additional resources for using the Manage Inc Admin Panel effectively.</p>

                    <h3>Documentation Resources</h3>
                    <ul>
                        <li><strong>User Guide</strong> - Comprehensive guides for all features and functionality</li>
                        <li><strong>Video Tutorials</strong> - Step-by-step video guides for common tasks</li>
                        <li><strong>Feature Documentation</strong> - Detailed documentation for each admin panel feature</li>
                        <li><strong>Best Practices</strong> - Tips and recommendations for optimal usage</li>
                        <li><strong>Release Notes</strong> - Information about new features and updates</li>
                    </ul>

                    <h3>Getting Help</h3>
                    <p>When you need assistance:</p>
                    <ol>
                        <li><strong>Check the Documentation</strong> - Most questions are answered in this help system</li>
                        <li><strong>Search the FAQ</strong> - Look for answers to common questions</li>
                        <li><strong>Contact Your Administrator</strong> - Reach out to your system administrator for technical issues</li>
                        <li><strong>Check System Status</strong> - Verify if there are any known system issues</li>
                    </ol>

                    <h3>Training Resources</h3>
                    <p>Improve your skills with these learning resources:</p>
                    <ul>
                        <li><strong>Quick Start Guide</strong> - Get up and running quickly with essential features</li>
                        <li><strong>Feature Tutorials</strong> - Learn specific features in detail</li>
                        <li><strong>Best Practices Guide</strong> - Learn optimal workflows and techniques</li>
                        <li><strong>Advanced Features</strong> - Master complex functionality and customization</li>
                    </ul>

                    <h3>System Information</h3>
                    <p>Important information about your system:</p>
                    <ul>
                        <li><strong>Version Information</strong> - Current system version and update status</li>
                        <li><strong>System Requirements</strong> - Browser and technical requirements</li>
                        <li><strong>Security Guidelines</strong> - Best practices for keeping your system secure</li>
                        <li><strong>Backup Information</strong> - Understanding your backup and recovery options</li>
                    </ul>

                    <h3>Contact Information</h3>
                    <p>For technical support and assistance:</p>
                    <ul>
                        <li><strong>System Administrator</strong> - Contact your local system administrator for immediate help</li>
                        <li><strong>Technical Support</strong> - For system-level issues and configuration problems</li>
                        <li><strong>Training Requests</strong> - Request additional training or documentation</li>
                        <li><strong>Feature Requests</strong> - Suggest new features or improvements</li>
                    </ul>
                </div>
            </div>

            <!-- Analytics & Reports Section -->
            <div class="content-section" id="analytics">
                <h2 class="section-title">Analytics & Reports</h2>
                <div class="section-content">
                    <p>Monitor your website's performance and user activities with comprehensive analytics and reporting tools. Track important metrics and generate detailed reports.</p>

                    <h3>Dashboard Analytics</h3>
                    <p>Get an overview of your website's performance:</p>
                    <ul>
                        <li><strong>Visitor Statistics</strong>: Track unique visitors, page views, and session duration</li>
                        <li><strong>Content Performance</strong>: See which articles and pages are most popular</li>
                        <li><strong>User Activity</strong>: Monitor admin user activities and login patterns</li>
                        <li><strong>System Health</strong>: Check server performance and resource usage</li>
                        <li><strong>Real-time Data</strong>: View live visitor activity and system status</li>
                    </ul>

                    <h3>Content Reports</h3>
                    <p>Analyze your content performance:</p>
                    <ul>
                        <li><strong>Article Views</strong>: Track views, engagement, and popular content</li>
                        <li><strong>Category Performance</strong>: See which content categories perform best</li>
                        <li><strong>Search Terms</strong>: Monitor what visitors search for on your site</li>
                        <li><strong>Content Trends</strong>: Identify trending topics and content gaps</li>
                        <li><strong>Publishing Analytics</strong>: Track content creation and publishing patterns</li>
                    </ul>

                    <h3>User Activity Reports</h3>
                    <p>Monitor admin panel usage and user behavior:</p>
                    <ul>
                        <li><strong>Login Activity</strong>: Track user logins, failed attempts, and session duration</li>
                        <li><strong>Content Changes</strong>: Monitor who made what changes and when</li>
                        <li><strong>User Performance</strong>: See which users are most active</li>
                        <li><strong>Permission Usage</strong>: Track how different user roles use the system</li>
                        <li><strong>Security Events</strong>: Monitor security-related activities and alerts</li>
                    </ul>

                    <h3>System Performance</h3>
                    <p>Keep track of your system's health and performance:</p>
                    <ul>
                        <li><strong>Server Metrics</strong>: Monitor CPU, memory, and disk usage</li>
                        <li><strong>Database Performance</strong>: Track query performance and database size</li>
                        <li><strong>Page Load Times</strong>: Monitor website speed and performance</li>
                        <li><strong>Error Tracking</strong>: Identify and track system errors and issues</li>
                        <li><strong>Backup Status</strong>: Monitor backup success and storage usage</li>
                    </ul>

                    <h3>Custom Reports</h3>
                    <p>Generate detailed reports for specific needs:</p>
                    <ul>
                        <li><strong>Date Range Reports</strong>: Generate reports for specific time periods</li>
                        <li><strong>Export Options</strong>: Export reports in PDF, CSV, or Excel formats</li>
                        <li><strong>Scheduled Reports</strong>: Set up automatic report generation and delivery</li>
                        <li><strong>Custom Metrics</strong>: Create reports for specific KPIs and metrics</li>
                        <li><strong>Comparative Analysis</strong>: Compare performance across different time periods</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.sidebar-nav-link');
            const featureLinks = document.querySelectorAll('.feature-link');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileOverlay = document.getElementById('mobileOverlay');
            const sidebar = document.getElementById('sidebar');

            // Function to scroll to section and update active nav
            function scrollToSection(targetId) {
                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    // Smooth scroll to section
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update active navigation
                    navLinks.forEach(l => l.classList.remove('active'));
                    const targetNavLink = document.querySelector(`.sidebar-nav-link[href="#${targetId}"]`);
                    if (targetNavLink) {
                        targetNavLink.classList.add('active');
                    }

                    // Close mobile menu if open
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('open');
                        mobileOverlay.classList.remove('active');
                    }
                }
            }

            // Navigation link clicks
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    scrollToSection(targetId);
                });
            });

            // Feature card link clicks
            featureLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    scrollToSection(targetId);
                });
            });

            // Mobile menu toggle
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    mobileOverlay.classList.toggle('active');
                });
            }

            // Close mobile menu when overlay is clicked
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('open');
                    mobileOverlay.classList.remove('active');
                });
            }

            // Update active navigation based on scroll position
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.content-section');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === '#' + sectionId) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
