<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Inc Documentation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #f1ca2f;
            --primary-dark: #e0b929;
            --text-color: #1a1a1a;
            --text-muted: #6b7280;
            --border-color: #e5e7eb;
            --bg-color: #ffffff;
            --bg-secondary: #f9fafb;
            --sidebar-width: 280px;
            --header-height: 64px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            font-size: 16px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--header-height);
            background: var(--bg-color);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            padding: 0 24px;
            z-index: 1000;
        }

        .header-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            text-decoration: none;
        }

        .header-brand i {
            color: var(--primary-color);
            font-size: 24px;
        }

        .header-nav {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .header-link {
            color: var(--text-muted);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: color 0.2s;
        }

        .header-link:hover {
            color: var(--text-color);
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-color);
            cursor: pointer;
            margin-left: auto;
        }

        /* Main Layout */
        .container {
            display: flex;
            margin-top: var(--header-height);
            min-height: calc(100vh - var(--header-height));
        }

        /* Sidebar */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            position: fixed;
            height: calc(100vh - var(--header-height));
            left: 0;
            top: var(--header-height);
        }

        .sidebar-content {
            padding: 24px 0;
        }

        .sidebar-section {
            margin-bottom: 32px;
        }

        .sidebar-section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
            padding: 0 24px 12px;
            margin-bottom: 8px;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav-item {
            margin-bottom: 4px;
        }

        .sidebar-nav-link {
            display: block;
            padding: 8px 24px;
            color: var(--text-muted);
            text-decoration: none;
            font-size: 14px;
            line-height: 1.4;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .sidebar-nav-link:hover {
            color: var(--text-color);
            background-color: rgba(241, 202, 47, 0.05);
        }

        .sidebar-nav-link.active {
            color: var(--primary-color);
            background-color: rgba(241, 202, 47, 0.1);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 40px 48px;
            max-width: calc(100% - var(--sidebar-width));
        }

        .content-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 18px;
            color: var(--text-muted);
            line-height: 1.5;
        }

        /* Content Sections */
        .content-section {
            margin-bottom: 48px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 16px;
            line-height: 1.3;
        }

        .section-content {
            font-size: 16px;
            line-height: 1.7;
            color: var(--text-color);
        }

        .section-content p {
            margin-bottom: 16px;
        }

        .section-content h3 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
            margin: 32px 0 16px;
            line-height: 1.3;
        }

        .section-content h4 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin: 24px 0 12px;
            line-height: 1.3;
        }

        .section-content ul,
        .section-content ol {
            margin-bottom: 16px;
            padding-left: 24px;
        }

        .section-content li {
            margin-bottom: 8px;
        }

        .section-content a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .section-content a:hover {
            text-decoration: underline;
        }

        .section-content strong {
            font-weight: 600;
        }

        .section-content code {
            background: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        /* Feature Grid */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin: 32px 0;
        }

        .feature-card {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 24px;
            transition: all 0.2s;
        }

        .feature-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: rgba(241, 202, 47, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
        }

        .feature-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .feature-description {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .feature-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
        }

        .feature-link:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                padding: 32px 24px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .header-nav {
                display: none;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                z-index: 1001;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 24px 16px;
                max-width: 100%;
            }

            .page-title {
                font-size: 28px;
            }

            .page-subtitle {
                font-size: 16px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        /* Mobile Overlay */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: var(--header-height);
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .mobile-overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <a href="#" class="header-brand">
            <i class="fas fa-book"></i>
            Manage Inc Documentation
        </a>
        <nav class="header-nav">
            <a href="dashboard.php" class="header-link">Dashboard</a>
            <a href="../index.html" class="header-link">Back to Site</a>
        </nav>
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <!-- Main Container -->
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Introduction</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#introduction" class="sidebar-nav-link active">Introduction</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#getting-started" class="sidebar-nav-link">Getting Started</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#key-features" class="sidebar-nav-link">Key Features</a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Features</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#news-management" class="sidebar-nav-link">News Management</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#user-management" class="sidebar-nav-link">User Management</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#inbox" class="sidebar-nav-link">Inbox & Messages</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#frontend-editor" class="sidebar-nav-link">Frontend Editor</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#settings" class="sidebar-nav-link">System Settings</a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-section-title">Resources</h3>
                    <ul class="sidebar-nav">
                        <li class="sidebar-nav-item">
                            <a href="#tutorials" class="sidebar-nav-link">Tutorials</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#faq" class="sidebar-nav-link">FAQ</a>
                        </li>
                        <li class="sidebar-nav-item">
                            <a href="#support" class="sidebar-nav-link">Support</a>
                        </li>
                    </ul>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">Introduction</h1>
                <p class="page-subtitle">Welcome to the Manage Inc Admin Panel documentation. This guide will help you understand and effectively use all features of the admin system.</p>
            </div>

            <div class="content-section" id="what-is-manage-inc">
                <h2 class="section-title">What is Manage Inc Admin Panel?</h2>
                <div class="section-content">
                    <p>Manage Inc Admin Panel is a comprehensive content management system that helps you manage your website content, users, and system settings efficiently. Think of it as the control center for your website, bringing everything together in one intuitive interface.</p>

                    <p>Most admin panels are complex, difficult to use, and require technical expertise. Manage Inc is different. It's built to be user-friendly, powerful, and flexible. You get enterprise-level functionality without the usual complications.</p>
                </div>
            </div>

            <div class="content-section" id="why-manage-inc">
                <h2 class="section-title">Why Manage Inc?</h2>
                <div class="section-content">
                    <p>Because managing your website should be simple and efficient. Your admin panel should empower you, not frustrate you.</p>

                    <p>With Manage Inc Admin Panel, you get a complete set of tools for all your content management needs, built-in and ready to use. With Manage Inc, you get:</p>

                    <ol>
                        <li><strong>Complete content management</strong> – From news articles to page content, everything you need is built-in.</li>
                        <li><strong>User-friendly interface</strong> – A clean, modern interface that doesn't require technical expertise.</li>
                        <li><strong>Powerful editor</strong> – Professional WYSIWYG editor with advanced formatting options.</li>
                        <li><strong>Responsive design</strong> – Works perfectly on desktop, tablet, and mobile devices.</li>
                    </ol>

                    <p>Whether you're managing a small website or a large content portal, Manage Inc helps you stay organized, streamline your workflow, and focus on what really matters - creating great content.</p>
                </div>
            </div>

            <div class="content-section" id="key-features">
                <h2 class="section-title">Key Features</h2>
                <div class="section-content">
                    <p>Manage Inc Admin Panel will help you to:</p>

                    <ol>
                        <li>Create and manage news articles and blog posts</li>
                        <li>Edit website content with a professional WYSIWYG editor</li>
                        <li>Manage user accounts and permissions</li>
                        <li>Handle contact form submissions through the inbox</li>
                        <li>Configure system settings and preferences</li>
                        <li>Track user activities with comprehensive logging</li>
                        <li>Upload and manage media files</li>
                        <li>Customize website appearance and layout</li>
                        <li>Monitor system performance and analytics</li>
                        <li>Backup and restore your content</li>
                        <li>Manage email templates and notifications</li>
                        <li>Control access with role-based permissions</li>
                    </ol>

                    <p>And much more.</p>
                </div>
            </div>

            <div class="content-section" id="getting-started">
                <h2 class="section-title">Getting Started</h2>
                <div class="section-content">
                    <p>Ready to start using Manage Inc Admin Panel? Here's how to get up and running quickly:</p>

                    <ol>
                        <li><strong>Login</strong>: Access your admin panel using your provided credentials</li>
                        <li><strong>Dashboard</strong>: Familiarize yourself with the dashboard and main navigation</li>
                        <li><strong>First Content</strong>: Create your first news post or edit existing content</li>
                        <li><strong>Settings</strong>: Configure your system settings and preferences</li>
                        <li><strong>Users</strong>: Set up additional user accounts if needed</li>
                    </ol>

                    <p>For detailed step-by-step instructions, check out our <a href="#tutorials">tutorials section</a>.</p>
                </div>
            </div>

            <div class="content-section" id="feature-overview">
                <h2 class="section-title">Feature Overview</h2>
                <div class="section-content">
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <h3 class="feature-title">News Management</h3>
                            <p class="feature-description">Create, edit, and organize news posts and articles with our intuitive content management system.</p>
                            <a href="#news-management" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="feature-title">User Management</h3>
                            <p class="feature-description">Manage user accounts, roles, and permissions with comprehensive user administration tools.</p>
                            <a href="#user-management" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h3 class="feature-title">Inbox & Messages</h3>
                            <p class="feature-description">Handle contact form submissions and manage communications in one centralized location.</p>
                            <a href="#inbox" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3 class="feature-title">Frontend Editor</h3>
                            <p class="feature-description">Edit website content directly with our professional WYSIWYG editor and code view options.</p>
                            <a href="#frontend-editor" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h3 class="feature-title">System Settings</h3>
                            <p class="feature-description">Configure system-wide settings, email preferences, and customize your admin experience.</p>
                            <a href="#settings" class="feature-link">Learn more →</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3 class="feature-title">Analytics & Reports</h3>
                            <p class="feature-description">Monitor system performance and user activities with comprehensive reporting tools.</p>
                            <a href="#analytics" class="feature-link">Learn more →</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-section" id="support">
                <h2 class="section-title">Learning and Support</h2>
                <div class="section-content">
                    <ol>
                        <li><strong>Documentation</strong> - Comprehensive guides and tutorials for all features</li>
                        <li><strong>FAQ</strong> - Answers to frequently asked questions</li>
                        <li><strong>Tutorials</strong> - Step-by-step instructions for common tasks</li>
                        <li><strong>Support</strong> - Contact your system administrator for technical assistance</li>
                    </ol>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.sidebar-nav-link');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileOverlay = document.getElementById('mobileOverlay');
            const sidebar = document.getElementById('sidebar');

            // Navigation link clicks
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Get target section
                    const targetId = this.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        // Smooth scroll to section
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }

                    // Close mobile menu if open
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('open');
                        mobileOverlay.classList.remove('active');
                    }
                });
            });

            // Mobile menu toggle
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('open');
                    mobileOverlay.classList.toggle('active');
                });
            }

            // Close mobile menu when overlay is clicked
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('open');
                    mobileOverlay.classList.remove('active');
                });
            }

            // Update active navigation based on scroll position
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.content-section');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === '#' + sectionId) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
